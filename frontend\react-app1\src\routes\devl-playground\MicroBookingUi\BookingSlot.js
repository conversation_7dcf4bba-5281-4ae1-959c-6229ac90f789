import React, { useState, useEffect } from 'react';
import {
    Card,
    Checkbox,
    Select,
    Row,
    Col,
    Button,
    message,
    Spin,
    Alert,
    Tag,
    Radio,
} from 'antd';
import moment from 'moment';
import http_utils from '../../../util/http_utils';
import './booking.css';

const { Option } = Select;

const BookingSlotUp = () => {
    const [isBookingRequired, setIsBookingRequired] = useState(false);
    const [selectedWeek, setSelectedWeek] = useState(null);
    const [weekOptions, setWeekOptions] = useState([]);
    const [weekData, setWeekData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [selectedSlots, setSelectedSlots] = useState({});
    const [error, setError] = useState(null);

    // Generate week options
    useEffect(() => {
        generateWeekOptions();
    }, []);

    const generateWeekOptions = () => {
        const weeks = [];
        const today = moment();

        // First week: today to upcoming Saturday
        const firstWeekEnd = today.clone().day(6); // 6 = Saturday
        if (firstWeekEnd.isBefore(today)) {
            firstWeekEnd.add(1, 'weeks'); // if today is Sunday, get next Saturday
        }

        weeks.push({
            value: `${today.format('YYYY-MM-DD')}_${firstWeekEnd.format('YYYY-MM-DD')}`,
            label: `${today.format('Do MMM')} - ${firstWeekEnd.format('Do MMM ')}`,
            startDate: today.format('YYYY-MM-DD'),
            endDate: firstWeekEnd.format('YYYY-MM-DD'),
        });

        // Generate next 4 weeks (Sun–Sat)
        const nextWeekStart = firstWeekEnd.clone().add(1, 'days'); // Sunday after first Saturday

        for (let i = 0; i < 4; i++) {
            const weekStart = nextWeekStart.clone().add(i * 7, 'days');
            const weekEnd = weekStart.clone().add(6, 'days');

            weeks.push({
                value: `${weekStart.format('YYYY-MM-DD')}_${weekEnd.format('YYYY-MM-DD')}`,
                label: `${weekStart.format('Do MMM')} - ${weekEnd.format('Do MMM ')}`,
                startDate: weekStart.format('YYYY-MM-DD'),
                endDate: weekEnd.format('YYYY-MM-DD'),
            });
        }

        setWeekOptions(weeks);
    };

    // Fetch org settings to get generated slots
    const fetchOrgSettings = () => {
        if (loading) return;

        setLoading(true);
        setViewData(undefined);
        setError(undefined);

        const onComplete = (resp) => {
            console.log('Availability slots response:', resp);
            setViewData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            console.error('Error fetching availability slots:', error);
            setViewData(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/setup/capacity/availability-slots/proto',
            {},
            onComplete,
            onError
        );
    };

    // Fetch week data when week is selected
    const fetchWeekData = async (weekValue) => {
        const selectedWeekOption = weekOptions.find(
            (w) => w.value === weekValue
        );
        if (!selectedWeekOption) return;

        try {
            setLoading(true);

            // Generate days for the selected week
            const startDate = moment(selectedWeekOption.startDate);
            const endDate = moment(selectedWeekOption.endDate);
            const days = [];

            let currentDate = startDate.clone();
            while (currentDate.isSameOrBefore(endDate)) {
                days.push({
                    date: currentDate.format('YYYY-MM-DD'),
                    dayName: currentDate.format('dddd'),
                    displayDate: currentDate.format('Do MMM'),
                });
                currentDate.add(1, 'day');
            }

            // Call API to get week-specific booking data (this would be a real API call)
            // For now, we'll just simulate it
            console.log(
                'Fetching booking data for week:',
                selectedWeekOption.startDate,
                'to',
                selectedWeekOption.endDate
            );

            // You can uncomment this to make an actual API call for week data
            /*
            http_utils.performGetCall(
                '/my-availability/range',
                {
                    start_date: selectedWeekOption.startDate,
                    end_date: selectedWeekOption.endDate
                },
                (weekApiData) => {
                    console.log('Week booking data:', weekApiData);
                    // Process the API response here
                },
                (error) => {
                    console.error('Error fetching week booking data:', error);
                }
            );
            */

            setWeekData({
                ...selectedWeekOption,
                days: days,
            });

            // Initialize selected slots for each day
            const initialSlots = {};
            days.forEach((day) => {
                initialSlots[day.date] = [];
            });
            setSelectedSlots(initialSlots);
        } catch (error) {
            console.error('Error fetching week data:', error);
            message.error('Failed to fetch week data');
        } finally {
            setLoading(false);
        }
    };

    // Handle week selection
    const handleWeekSelect = (value) => {
        setSelectedWeek(value);
        fetchWeekData(value);
    };

    // Handle slot selection for a specific day
    const handleSlotSelection = (date, slotValue) => {
        setSelectedSlots((prev) => {
            const daySlots = prev[date] || [];
            const isSelected = daySlots.includes(slotValue);

            return {
                ...prev,
                [date]: isSelected
                    ? daySlots.filter((slot) => slot !== slotValue)
                    : [...daySlots, slotValue],
            };
        });
    };

    // Get generated slots from org settings
    const getGeneratedSlots = () => {
        if (!viewData || !viewData.form_data.generated_slots) {
            // Return demo slots if org settings not available
            return [
                { value: '09:00AM - 10:00AM', label: '09:00AM - 10:00AM' },
                { value: '10:00AM - 11:00AM', label: '10:00AM - 11:00AM' },
                { value: '11:00AM - 12:00PM', label: '11:00AM - 12:00PM' },
                { value: '12:00PM - 01:00PM', label: '12:00PM - 01:00PM' },
                { value: '01:00PM - 02:00PM', label: '01:00PM - 02:00PM' },
                { value: '02:00PM - 03:00PM', label: '02:00PM - 03:00PM' },
                { value: '03:00PM - 04:00PM', label: '03:00PM - 04:00PM' },
                { value: '04:00PM - 05:00PM', label: '04:00PM - 05:00PM' },
            ];
        }

        return viewData.form_data.generated_slots;
    };

    const handleBookingRequiredChange = (value) => {
        setIsBookingRequired(value);

        if (value) {
            // Fetch org settings when booking is required
            fetchOrgSettings();
        } else {
            // Reset dependent data if user selects "No"
            setSelectedWeek(null);
            setSelectedSlots({});
            setWeekData(null);
        }
    };

    // Handle save selected slots
    const handleSaveSlots = () => {
        const totalSelectedSlots = Object.values(selectedSlots).reduce(
            (total, daySlots) => total + daySlots.length,
            0
        );

        if (totalSelectedSlots === 0) {
            message.warning('Please select at least one slot');
            return;
        }

        console.log('Selected slots:', selectedSlots);
        message.success(
            `Successfully saved ${totalSelectedSlots} slots across ${Object.keys(selectedSlots).filter((date) => selectedSlots[date].length > 0).length} days`
        );
    };

    if (isBookingRequired && !viewData) {
        return (
            <div>
                <p className="gx-text-red">{error}</p>
            </div>
        );
    }

    return (
        <div className="booking-wrapper">
            {/* Booking Required - Radio Buttons */}
            <div className="gx-mb-4">
                <div
                    className="gx-mb-2 gx-text-bold"
                    style={{
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: '#333',
                    }}
                >
                    BOOKING REQUIRED:
                </div>
                <Radio.Group
                    value={isBookingRequired ? 'yes' : 'no'}
                    onChange={(e) =>
                        handleBookingRequiredChange(e.target.value === 'yes')
                    }
                >
                    <Radio value="yes">Yes</Radio>
                    <Radio value="no">No</Radio>
                </Radio.Group>
            </div>

            {isBookingRequired && viewData && (
                <>
                    {Object.values(selectedSlots).every(
                        (slots) => slots.length === 0
                    ) && (
                        <div className="gx-mb-4">
                            <div
                                className="gx-mb-2"
                                style={{
                                    fontSize: '16px',
                                    fontWeight: 'bold',
                                    color: '#666',
                                }}
                            >
                                SELECT A SLOT
                            </div>
                            <div
                                className="gx-mb-2"
                                style={{ fontSize: '14px', color: '#666' }}
                            >
                                Select week:
                            </div>
                            <div className="week-selector-container">
                                <Select
                                    placeholder="Select a week"
                                    style={{
                                        width: '100%',
                                        borderRadius: '4px',
                                    }}
                                    value={selectedWeek}
                                    onChange={handleWeekSelect}
                                    loading={loading}
                                    bordered={false}
                                >
                                    {weekOptions.map((week) => (
                                        <Option
                                            key={week.value}
                                            value={week.value}
                                        >
                                            {week.label}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        </div>
                    )}

                    {weekData && (
                        <div className="gx-mb-4 gx-ml-2">
                            {loading ? (
                                <div className="gx-text-center gx-p-4">
                                    <Spin size="large" />
                                </div>
                            ) : (
                                <>
                                    {Object.values(selectedSlots).every(
                                        (slots) => slots.length === 0
                                    ) && (
                                        <Card className="booking-scroll-card">
                                            <div className="booking-scroll-container">
                                                {weekData.days.map((day) => (
                                                    <div
                                                        key={day.date}
                                                        className="booking-day-card"
                                                    >
                                                        <div className="booking-day-header">
                                                            {day.dayName} <br />
                                                            {day.displayDate}
                                                        </div>

                                                        <div className="slot-grid">
                                                            {getGeneratedSlots()?.map(
                                                                (slot) => {
                                                                    const isSelected =
                                                                        selectedSlots[
                                                                            day
                                                                                .date
                                                                        ]?.includes(
                                                                            slot.value
                                                                        );

                                                                    return (
                                                                        <div
                                                                            key={`${day.date}-${slot.value}`}
                                                                            onClick={() =>
                                                                                handleSlotSelection(
                                                                                    day.date,
                                                                                    slot.value
                                                                                )
                                                                            }
                                                                            className={`slot-box ${isSelected ? 'slot-box-selected' : ''}`}
                                                                        >
                                                                            <div>
                                                                                {slot.label ||
                                                                                    slot.value}
                                                                            </div>
                                                                            {isSelected && (
                                                                                <div className="slot-label-tag">
                                                                                    #
                                                                                    SLOTS
                                                                                    LEFT
                                                                                    1
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    );
                                                                }
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </Card>
                                    )}

                                    {/* Show selected slots summary */}
                                    {Object.values(selectedSlots).some(
                                        (slots) => slots.length > 0
                                    ) && (
                                        <div className="selected-slot-summary">
                                            <div
                                                style={{
                                                    fontSize: '18px',
                                                    fontWeight: 'bold',
                                                    marginBottom: '12px',
                                                    color: '#333',
                                                }}
                                            >
                                                SELECTED SLOT
                                            </div>

                                            {Object.entries(selectedSlots).map(
                                                ([date, slots]) => {
                                                    if (slots.length === 0)
                                                        return null;

                                                    const day =
                                                        weekData.days.find(
                                                            (d) =>
                                                                d.date === date
                                                        );

                                                    return (
                                                        <div
                                                            className="selected-slot-card gx-border-2 gx-border-green"
                                                            key={date}
                                                        >
                                                            <div className="selected-slot-day">
                                                                {day?.dayName}{' '}
                                                                {
                                                                    day?.displayDate
                                                                }
                                                            </div>
                                                            <hr className="selected-slot-divider" />
                                                            <div className="selected-slot-tags">
                                                                {slots.map(
                                                                    (slot) => (
                                                                        <Tag
                                                                            key={
                                                                                slot
                                                                            }
                                                                            color="success"
                                                                            className="selected-slot-tag"
                                                                        >
                                                                            {
                                                                                slot
                                                                            }
                                                                        </Tag>
                                                                    )
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                }
                                            )}
                                        </div>
                                    )}
                                    <div className="gx-mt-4 gx-text-center">
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={handleSaveSlots}
                                            disabled={Object.values(
                                                selectedSlots
                                            ).every(
                                                (daySlots) =>
                                                    daySlots.length === 0
                                            )}
                                        >
                                            Save Selected Slots
                                        </Button>
                                    </div>
                                </>
                            )}
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default BookingSlotUp;
