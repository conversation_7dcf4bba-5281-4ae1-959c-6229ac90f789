import React, { Component } from 'react';
import {
    Modal,
    Form,
    Radio,
    Input,
    Button,
    Popover,
    Steps,
    message,
    Row,
    Col,
    Avatar,
    Tooltip,
    Checkbox,
    Typography,
    Upload,
    Timeline,
    List,
    Spin,
    Alert,
    Collapse,
    Rate,
    Select,
    Tabs,
    notification,
    Tag,
} from 'antd';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../util/http_utils';
import CircularProgress from '../../components/CircularProgress';
import {
    AntDesignOutlined,
    EllipsisOutlined,
    InboxOutlined,
    PlusOutlined,
    RightOutlined,
    ShareAltOutlined,
    UploadOutlined,
    UserOutlined,
    HistoryOutlined,
    PhoneOutlined,
    CopyOutlined,
    QuestionCircleOutlined,
} from '@ant-design/icons';
// import ReactTags from 'react-tag-autocomplete'
import Dragger from 'antd/lib/upload/Dragger';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import {
    convertDateFieldsToMoments,
    getTouchedFieldsValueInForm,
    priorities,
    getGeneralFileSection,
    hasAnyFileChanged,
    NoData,
    PROJECT_BASED_SERVICE_TYPE,
    getLabel,
    getSkillLevelOptions,
    isMobileView,
    isScreenZoomPercentage125,
    readFromLocalStorage,
    getUserRightsFrService,
} from '../../util/helpers';
import CompactSubtasksList from '../../components/WIFY/subtasks/CompactSubtasksList';
import TimelineCard, {
    getAppCallInfoMeta,
    getApprovalReqStatusChangeInfoMeta,
    getAutoAuthoritiesRoleInfoMeta,
    getCommentstInfoMeta,
    getServiceProviderMeta,
} from './TimelineCard';
import StatusChanger from './StatusChanger';
import UserSelectorWidget from '../../components/wify-utils/UserSelectorWidget';
import S3Uploader from '../../components/wify-utils/S3Uploader/S3Uploader';
import { createAntdFormMeta } from '../../components/wify-utils/FieldCreator/mapping_form_builder';
import {
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
    decodeMicSectionsFrmJson,
    decodeCameraSectionsFrmJson,
} from '../../components/wify-utils/FieldCreator/helpers';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import { debounce } from 'lodash';
import {
    getAddressFieldsMeta,
    getAddressFieldsMetaTMS250312325161,
    getCustomerDetailsFrmApi,
} from '../../util/CustomerHelpers';
import FeedbackStars from './FeedbackStars';
import SrvcPrvdrSelector from './SrvcPrvdrSelector';
import CustomerHistory from '../../components/WIFY/CustomerHistory';
import moment from 'moment';
import StatusHistory from './StatusHistory';
import ConfigHelpers from '../../util/ConfigHelpers';
import MicInputV2 from '../../components/wify-utils/MicInput_v2';
import AuthoritySelector from './AuthoritySelector';
import DeleteSrvcReqButton from './DeleteSrvcRequest';
import CameraInput from '../../components/wify-utils/CameraInput';
import CopyToClipboard from 'react-copy-to-clipboard';
import MetaInputTable from '../../components/wify-utils/MetaInputTable';
import LineItems from './LineItems';
import SrvcReqCalendar from './Calendar';
import Deployments from './project/deployment/Deployments';
import LocationSearchInput from '../../components/LocationSearchInput';
import Billing from './project/billing/Billing';
import { getIsSrvcReqLock } from './project/billing/helpers';
import {
    checkUserAccessInAuthorities,
    getAvgGaiRatingOfSubTasks,
    getSbtskTimeFieldsMetaFrReqCreation,
    getSrvcCategoryFieldOptions,
    getSrvcReqCalendarData,
    showExtOrderId,
} from './helpers';
import RefreshLocationGrp from './RefreshLocationGrp';
import Rating from './Rating';
import SubtaskFilesSection from './subtaskFilesSection/SubtaskFilesSection';
import GaiWrapper from '../../components/wify-utils/Gai/GaiWrapper';
import LifetimeStatusHistory from './LifetimeStatusHistory';
import SpPayouts from './SpPayouts';
import StarRatingCompact from '../../components/WIFY/WifyComponents/StarRatingCompact';
import ProfitAndLoss from './ProfitAndLoss';
import BrandCard from './BrandCard';
import ProjectProfitAndLoss from './ProjectProfitAndLoss';
import checkFeatureAccess from '../../util/FeatureAccess';
import BookingSlotUp from '../devl-playground/MicroBookingUi/BookingSlot';
import PreSrvcPrvdrSelector from './PreSrvcPrvdrSelector';
// import {Paragraph} from 'antd';

const protoUrl = '/services/proto';
const submitUrl = '/services/modify'; // this is referred in Index.js of services also

const tagsProto = {
    tags: [
        { value: 1, label: 'Apples' },
        { value: 2, label: 'Pears' },
    ],
    suggestions: [
        { value: 3, label: 'Auto built 1' },
        { value: 4, label: 'Auto built 2' },
        { value: 5, label: 'Auto built 3' },
        { value: 6, label: 'Auto built 4' },
    ],
};

const dynamicFormLogicPath = '/services/exec-dynamic-form-logic';
let debouncer;
let touchFieldsKeys = [];
class ItemEditor extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        // console.log("Service type details",this.props.srvcDetails);
        this.initApiUrls(this.props);
    }
    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
        currentStep: 0,
        srvcDetails: this.props.srvcDetails,
        srvc_id: this.props.srvcDetails?.srvc_id,
        fileSections: [],
        filesBySection: {},
        micSections: [],
        micRecordingsBySection: {},
        sectionWiseUploaderReady: {},
        sectionWiseMicUploaderReady: {},
        isLoadingCustomerDetails: false,
        autoFillCustId: '',
        custMobNoFrHis: '',
        appCallBtnText: undefined,
        app_call_error: '',
        cameraSections: [],
        cameraRecordingsBySection: {},
        sectionWiseCameraUploaderReady: {},
        uploadErrors: [],
        lineItemsData: undefined,
        spPayoutsData: undefined,
        spDeductionData: undefined,
        splineItemsData: undefined,
        canCLearAddress: false,
        additionalBillingItem: undefined,
        finalBillingItem: undefined,
        spAdditionalBillingItem: undefined,
        spFinalBillingItem: undefined,
        lastChangedFileSectionIds: [],
        selectedCurrentTab: 'request_details',
        transitionOverviewActiveTab: 'latest',
        calendarData: undefined,
        authoritySelectorFieldVsValue: {},
        // dynamic custom fields form
        manipulatedDynamicMeta: undefined,
        isExecutingDynamicFormLogic: false,
        locGrpsName: undefined,
        updateBillingData: false,
        isManipulatedDynamicSpFieldMeta: false,
        showOrgDetails: false,
        capacityData: undefined,
        isLoadingCapacityData: false,
    };

    state = this.initState;

    initApiUrls(props) {
        this.protoUrl = protoUrl + '/' + props.srvcDetails?.srvc_id;
        if (this.state.editMode) {
            this.submitUrl = submitUrl + '/' + props.srvcDetails?.srvc_id;
        } else {
            this.submitUrl =
                submitUrl +
                '/' +
                props.srvcDetails?.srvc_id +
                '/' +
                (this.props.isCustAcces ? '1' : '0');
        }

        // Initialize capacity API URL
        this.capacityApiUrl = '/services/capacity';
    }

    checkUserSettingPreference() {
        const setting = readFromLocalStorage({ key: 'user_settings' });
        if (setting?.show_org_details) {
            this.setState({
                showOrgDetails: setting.show_org_details,
            });
        }
    }

    verifyFeatureAccess = async () => {
        try {
            let hasAccess = await checkFeatureAccess('TMS250303062278');
            this.setState({ TMS250303062278: hasAccess });
        } catch (error) {
            this.setState({ TMS250303062278: false });
            console.error(
                'site attendance :: verifyFeatureAccess :: error : ',
                error
            );
        }
    };

    verifyFeatureAccessTMS250620631024 = async () => {
        try {
            let hasAccess = await checkFeatureAccess('TMS250620631024');
            this.setState({ TMS250620631024: hasAccess });
        } catch (error) {
            this.setState({ TMS250620631024: false });
            console.error(
                'SrvcTypeEditor :: verifyFeatureAccessTMS250620631024 :: error : ',
                error
            );
        }
    };
    verifyFeatureAccessTMS250312325161 = async () => {
        try {
            let hasAccess = await checkFeatureAccess('TMS250312325161');
            this.setState({ TMS250312325161: hasAccess });
        } catch (error) {
            this.setState({ TMS250312325161: false });
            console.error(
                'SrvcTypeEditor :: verifyFeatureAccessTMS250312325161 :: error : ',
                error
            );
        }
    };

    componentDidMount() {
        touchFieldsKeys = [];
        this.initViewData();
        this.verifyFeatureAccess();
        this.verifyFeatureAccessTMS250312325161();
        this.verifyFeatureAccessTMS250620631024();

        // Fetch capacity data when modal opens
        if (!this.state.editMode) {
            this.fetchCapacityData();
        }
    }

    getCallBtnText() {
        return this.getIsCalling() ? 'Connecting...' : 'Call Now';
    }

    getIsCalling = () => {
        let key =
            'last_call_consumer_button_click_' + this.props.editorItem?.id;
        // get local storage value
        let app_call_date_time = localStorage.getItem(key);
        let current_date_time = Date.now();
        if (current_date_time - app_call_date_time < 30000) {
            return true;
        } else {
            return false;
        }
    };

    componentWillUpdate(nextProps, nextState) {
        this.initApiUrls(nextProps);
    }

    isCustomerRequests() {
        return this.props.isCustomerRequests;
    }

    fetchCapacityData() {
        console.log(
            'called-????????????????????',
            this.props.sp_config_data?.db_id
        );
        if (!this.state.editMode) {
            const verticalId = this.props.sp_config_data?.db_id;

            if (!verticalId) {
                console.warn('Vertical ID not found in sp_config_data');
                return;
            }

            // Set loading state
            this.setState({ isLoadingCapacityData: true });

            // Prepare API parameters
            const params = {
                srvc_type_id: this.props.srvcDetails?.srvc_id,
                vertical_id: verticalId,
                srvc_prvdr_id: this.props.srvcConfigData?.srvc_default_provider,
            };
            console.log('params', params);
            const onComplete = (resp) => {
                this.setState({
                    isLoadingCapacityData: false,
                    capacityData: resp.data,
                });
            };

            const onError = (error) => {
                console.error('Failed to fetch capacity data:', error);
                this.setState({
                    isLoadingCapacityData: false,
                    capacityData: null,
                });
            };

            // Make API call
            http_utils.performGetCall(
                this.capacityApiUrl,
                params,
                onComplete,
                onError
            );
        }
        // Get vertical ID from props.sp_config_data.settings_data.db.id
    }

    initViewData() {
        // console.log("Trying to init view Data",this.state.visible);
        this.checkUserSettingPreference();
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({ isLoadingViewData: true });
            var params = {};
            const onComplete = (resp) => {
                this.initConfigData(
                    resp,
                    // then set loading false
                    this.setState(
                        {
                            isLoadingViewData: false,
                            viewData: resp.data,
                            error: '',
                        },
                        () => {
                            if (this.isDynamicForm()) {
                                this.onFormValueChanged({}, {}); // CDM
                            }
                            if (
                                this.isSPFieldsDynamicForm() &&
                                ConfigHelpers.isServiceProvider() &&
                                this.props.editorItem?.id
                            ) {
                                this.onFormValueChanged({}, {}, true);
                            }
                        }
                    )
                );
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            var url = !this.state.editMode
                ? this.protoUrl
                : this.protoUrl + '/' + this.props.editorItem.id;
            // console.log(url);
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    /**
     * Sanitizes authority fields in the provided prefill form data by ensuring
     * that each authority field's value is among the valid options defined in the authFields metadata.
     *
     * If any authority field (e.g., `authority_126`, `authority_127`, etc.) in `prefillFormData`
     * contains a value not present in the corresponding `authFieldsMeta.options`, that value is cleared (set to an empty string).
     *
     * @param {Object} prefillFormData - The original form data object which may contain authority fields.
     * @param {Array<Object>} authFieldsMeta - An array of metadata objects for each authority field.
     *        Each object must include:
     *          - `key` {string}: the authority field key (e.g., 'authority_126'),
     *          - `options` {Array<Object>}: an array of valid option objects, each containing:
     *              - `value` {string}: the valid ID value for the authority field.
     *
     * @returns {Object} A new object containing the sanitized form data.
     *
     * @example
     * const prefillFormData = {
     *   authority_126: "invalid-user-id",
     *   authority_127: "73b46de0-cd11-49ce-af15-9f107ac6b782"
     * };
     *
     * const authFieldsMeta = [
     *   {
     *     key: "authority_126",
     *     options: [{ value: "valid-id-1" }]
     *   },
     *   {
     *     key: "authority_127",
     *     options: [{ value: "73b46de0-cd11-49ce-af15-9f107ac6b782" }]
     *   }
     * ];
     *
     * const cleanedData = sanitizeAuthorityFields(prefillFormData, authFieldsMeta);
     * // cleanedData = {
     * //   authority_126: '',
     * //   authority_127: '73b46de0-cd11-49ce-af15-9f107ac6b782'
     * // }
     */

    sanitizeAuthorityFields(prefillFormData, authFieldsMeta) {
        if (!prefillFormData) {
            return prefillFormData;
        }
        if (!authFieldsMeta) {
            return prefillFormData;
        }
        const updatedFormData = { ...prefillFormData };

        authFieldsMeta.forEach((authField) => {
            const key = authField.key;
            const validOptionValues = authField.options.map(
                (option) => option.value
            );
            const currentValue = prefillFormData[key];

            if (currentValue && !validOptionValues.includes(currentValue)) {
                updatedFormData[key] = ''; // or delete updatedFormData[key];
            }
        });

        return updatedFormData;
    }

    initConfigData(entry_specific_data, then) {
        let newFileSections = [getGeneralFileSection()];
        let custom_file_sections = this.getCustomFileSectionsFrmConfig();

        let disableFieldsFrPrvdr =
            this.props.srvcConfigData
                ?.make_custom_fields_non_editable_from_provider;
        let disableFieldsArr =
            this.props.srvcConfigData
                ?.select_custom_fields_to_be_made_non_editable || [];

        if (disableFieldsFrPrvdr && ConfigHelpers.isServiceProvider()) {
            custom_file_sections.forEach((singleEntry) => {
                // Check if the key is in the disableFieldsArr
                if (disableFieldsArr.includes(singleEntry.key)) {
                    // Add the "disabled" property to singleEntry
                    singleEntry.disabled = true;
                }
            });
        }
        if (custom_file_sections && custom_file_sections.length > 0) {
            newFileSections = [...newFileSections, ...custom_file_sections];
        }
        // console.log('newFileSections',newFileSections)

        // Prefilling attachments
        let initialFilesBySection = {};

        if (entry_specific_data.data.form_data?.form_data) {
            initialFilesBySection =
                entry_specific_data.data.form_data?.form_data['attachments'];
        }
        if (Object.keys(this.state.filesBySection).length > 0) {
            initialFilesBySection = this.state.filesBySection;
        }

        let newMicSections = [];
        let customMicSections = this.getCustomMicSectionsFrmConfig();

        if (disableFieldsFrPrvdr && ConfigHelpers.isServiceProvider()) {
            customMicSections.forEach((singleEntry) => {
                // Check if the key is in the disableFieldsArr
                if (disableFieldsArr.includes(singleEntry.key)) {
                    // Add the "disabled" property to singleEntry
                    singleEntry.disabled = true;
                }
            });
        }
        if (customMicSections && customMicSections.length > 0) {
            newMicSections = [...newMicSections, ...customMicSections];
        }

        let initialMicRecordingsBySection = {};

        if (entry_specific_data.data.form_data?.form_data) {
            initialMicRecordingsBySection =
                entry_specific_data.data.form_data?.form_data['mic_files'];
        }
        if (Object.keys(this.state.micRecordingsBySection).length > 0) {
            initialMicRecordingsBySection = this.state.micRecordingsBySection;
        }

        let newCameraSections = [];
        let customCameraSections = this.getCustomCameraSectionsFrmConfig();

        if (disableFieldsFrPrvdr && ConfigHelpers.isServiceProvider()) {
            customCameraSections.forEach((singleEntry) => {
                // Check if the key is in the disableFieldsArr
                if (disableFieldsArr.includes(singleEntry.key)) {
                    // Add the "disabled" property to singleEntry
                    singleEntry.disabled = true;
                }
            });
        }
        if (customCameraSections && customCameraSections.length > 0) {
            newCameraSections = [...newCameraSections, ...customCameraSections];
        }

        let initialCameraRecordingsBySection = {};
        if (entry_specific_data.data.form_data?.form_data) {
            initialCameraRecordingsBySection =
                entry_specific_data.data.form_data?.form_data['camera_files'];
        }
        if (Object.keys(this.state.cameraRecordingsBySection).length > 0) {
            initialCameraRecordingsBySection =
                this.state.cameraRecordingsBySection;
        }

        //for service provider custom files section
        let sp_custom_file_sections = this.getSpCustomFileSectionsFrmConfig();
        if (sp_custom_file_sections && sp_custom_file_sections.length > 0) {
            newFileSections = [...newFileSections, ...sp_custom_file_sections];
        }

        //for sp camera files
        let sp_camera_sections = this.getSpCameraSectionsFrmConfig();
        if (sp_camera_sections && sp_camera_sections.length > 0) {
            newCameraSections = [...newCameraSections, ...sp_camera_sections];
        }

        //for sp mic section
        let sp_mic_sections = this.getSpMicSectionsFrmConfig();
        if (sp_mic_sections && sp_mic_sections.length > 0) {
            newMicSections = [...newMicSections, ...sp_mic_sections];
        }

        this.setState(
            {
                fileSections: newFileSections,
                filesBySection: { ...initialFilesBySection },
                micSections: newMicSections,
                micRecordingsBySection: { ...initialMicRecordingsBySection },
                cameraSections: newCameraSections,
                cameraRecordingsBySection: {
                    ...initialCameraRecordingsBySection,
                },
            },
            then
        );
    }

    getModifiedFileSectionsBasedOnFilesVsAuthorityMapping(customFileSections) {
        let modifiedFileSections = [...customFileSections];
        let fieldVsAuthorityMapping = this.getFieldVsAuthorityMapping();
        if (modifiedFileSections.length > 0) {
            modifiedFileSections.map((singleFileCustField) => {
                const authorizedUsersFrThisField =
                    fieldVsAuthorityMapping[singleFileCustField.key];
                if (
                    Array.isArray(authorizedUsersFrThisField) &&
                    authorizedUsersFrThisField.length > 0 &&
                    !authorizedUsersFrThisField.includes(
                        ConfigHelpers.getUserUUID()
                    )
                ) {
                    singleFileCustField['disabled'] = true;
                }
            });
        }
        return modifiedFileSections;
    }
    getCustomFileSectionsFrmConfig() {
        let custonFileSections =
            this.getModifiedFileSectionsBasedOnFilesVsAuthorityMapping(
                decodeFileSectionsFrmJson(this.getCustomFieldsJson())
            );
        return custonFileSections;
    }

    getCustomMicSectionsFrmConfig() {
        return decodeMicSectionsFrmJson(this.getCustomFieldsJson());
    }

    getCustomCameraSectionsFrmConfig() {
        return decodeCameraSectionsFrmJson(this.getCustomFieldsJson());
    }

    getSpCustomFileSectionsFrmConfig() {
        let spCustonFileSections =
            this.getModifiedFileSectionsBasedOnFilesVsAuthorityMapping(
                decodeFileSectionsFrmJson(
                    this.state.viewData?.form_data?.sp_config_data?.[0]
                        ?.settings_data?.sp_cust_fields_json
                )
            );
        let spHiddenFieldIds = this.props.sp_hidden_fields || [];
        // Concatenate hiddenFields and spHiddenFieldIds
        let modifySpCustomeFileSection = spCustonFileSections.filter(
            (singleField) =>
                !ConfigHelpers.isFieldHiddenFrUser(
                    singleField.key,
                    spHiddenFieldIds
                )
        );
        return modifySpCustomeFileSection;
    }

    getSpCameraSectionsFrmConfig() {
        return decodeCameraSectionsFrmJson(
            this.state.viewData?.form_data?.sp_config_data?.[0]?.settings_data
                ?.sp_cust_fields_json
        );
    }

    getSpMicSectionsFrmConfig() {
        return decodeMicSectionsFrmJson(
            this.state.viewData?.form_data?.sp_config_data?.[0]?.settings_data
                ?.sp_cust_fields_json
        );
    }

    isDynamicForm() {
        // return false;
        return this.props.srvcConfigData?.srvc_is_cust_fields_dynamic;
    }

    isSPFieldsDynamicForm() {
        return this.state.viewData?.sp_config_data[0]?.settings_data
            ?.is_custom_fields_dynamic;
    }

    getCustomFieldsJson() {
        if (this.state.manipulatedDynamicMeta) {
            return JSON.stringify({
                translatedFields: this.state.manipulatedDynamicMeta.filter(
                    (singleFieldMeta) => singleFieldMeta.hide != true
                ),
            });
        }
        return this.props.srvcConfigData?.srvc_cust_fields_json;
    }

    getAllDynamicCustomFieldsJson() {
        if (this.state.manipulatedDynamicMeta) {
            // Get the selected fields array
            const specificCollapsableFileds =
                this.getSelectedCollapsableSpecificFields(['open']);
            const openStatusCollapsableFields =
                this.getOpenStatusCollapsableSpecificFields();
            // Filter out objects whose keys don't match with specificCollapsableFileds
            const filteredMeta = this.state.manipulatedDynamicMeta.filter(
                (singleManipulatedDymaicField) => {
                    if (
                        !specificCollapsableFileds.includes(
                            singleManipulatedDymaicField.key
                        ) ||
                        (specificCollapsableFileds.includes(
                            singleManipulatedDymaicField.key
                        ) &&
                            openStatusCollapsableFields.includes(
                                singleManipulatedDymaicField.key
                            ))
                    ) {
                        return singleManipulatedDymaicField;
                    }
                }
            );
            return JSON.stringify({ translatedFields: filteredMeta });
        }
        return this.props.srvcConfigData?.srvc_cust_fields_json;
    }

    getCustomFieldJsonFrBulk(isFrmBulkCreation = true) {
        let columns =
            this.props.srvcConfigData?.srvc_cust_fields_json_colspan || 2;
        let customFields = decodeFieldsMetaFrmJson(
            this.getAllDynamicCustomFieldsJson(),
            columns,
            false,
            true,
            this.formRef
        );
        // console.log("getCustomFieldsFrmConfig customFields",customFields);
        let fieldVsAuthorityMapping = this.getFieldVsAuthorityMapping();

        // console.log('fieldVsAuthorityMapping',fieldVsAuthorityMapping);
        if (customFields) {
            customFields.map((singleCustFields, index) => {
                if (singleCustFields?.widget == 'select') {
                    if (singleCustFields?.widgetProps?.mode == 'multiple') {
                        singleCustFields['widgetProps'] = {
                            mode: 'multiple',
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    } else {
                        if (
                            singleCustFields?.key ==
                            this.props?.srvcConfigData?.srvc_category_field
                        ) {
                            singleCustFields['onChange'] = () => {
                                this.forceUpdate();
                            };
                            const selectedCategoryFieldValue =
                                this.formRef?.current?.getFieldValue(
                                    `${this.props?.srvcConfigData?.srvc_category_field}`
                                );
                            if (selectedCategoryFieldValue) {
                                let options = getSrvcCategoryFieldOptions(
                                    this?.props?.srvcConfigData,
                                    false
                                );
                                customFields.splice(index + 1, 0, {
                                    key: `skill_level_fr_${selectedCategoryFieldValue}`,
                                    label: `Skill level for ${getLabel(selectedCategoryFieldValue, options)}`,
                                    widget: 'select',
                                    required: true,
                                    options: getSkillLevelOptions(),
                                });
                            }
                        }
                        singleCustFields['widgetProps'] = {
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    }
                }
                const authorizedUsersFrThisField =
                    fieldVsAuthorityMapping[singleCustFields.key];
                if (
                    Array.isArray(authorizedUsersFrThisField) &&
                    authorizedUsersFrThisField.length > 0 &&
                    !authorizedUsersFrThisField.includes(
                        ConfigHelpers.getUserUUID()
                    )
                ) {
                    singleCustFields['widgetProps'] = {
                        ...(singleCustFields['widgetProps'] || {}),
                        disabled: true,
                    };
                }
            });
        }

        let disableFieldsFrPrvdr =
            this.props.srvcConfigData
                ?.make_custom_fields_non_editable_from_provider;
        let disableFieldsArr =
            this.props.srvcConfigData
                ?.select_custom_fields_to_be_made_non_editable || [];
        if (
            disableFieldsFrPrvdr &&
            ConfigHelpers.isServiceProvider() &&
            this.props.isCustAcces
        ) {
            customFields = customFields.filter(
                (obj) => !disableFieldsArr.includes(obj.key)
            );
        }
        let srvcPrvdrAuthority = [];
        if (
            ConfigHelpers.isSrvcPrvdrBySrvcReqId(this.state.viewData?.form_data)
        ) {
            srvcPrvdrAuthority = [...(this.getSPAuthoritySelectorMeta() || [])];
        }
        let brandAuthority = [];
        if (
            (!ConfigHelpers.isSrvcPrvdrBySrvcReqId(
                this.state.viewData?.form_data
            ) ||
                !this.isCustomerRequests()) &&
            (isFrmBulkCreation ||
                !this.props.srvcConfigData?.[
                    'hide_authority_selection_from_specific_details'
                ])
        ) {
            brandAuthority = [...(this.getAuthoritySelectorMeta() || [])];
        }
        const authoritySelectorMeta = [
            ...brandAuthority,
            ...srvcPrvdrAuthority,
        ];
        authoritySelectorMeta.forEach((singleMeta) => {
            singleMeta['widgetProps'] = {
                showSearch: true,
                optionFilterProp: 'children',
            };
        });
        const selectedSpCategoryFieldValue =
            this.formRef?.current?.getFieldValue(
                `${this.state.viewData?.sp_config_data[0]?.settings_data.srvc_prvdr_category_field}`
            );
        const spcustomFields = [...(this.getSpCustomFieldsMeta() || [])];
        if (spcustomFields) {
            spcustomFields.map((singleSpCustFields, index) => {
                if (singleSpCustFields?.widget == 'select') {
                    if (singleSpCustFields?.widgetProps?.mode == 'multiple') {
                        singleSpCustFields['widgetProps'] = {
                            mode: 'multiple',
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    } else {
                        if (
                            singleSpCustFields?.key ==
                            this.state.viewData?.sp_config_data[0]
                                ?.settings_data.srvc_prvdr_category_field
                        ) {
                            singleSpCustFields['onChange'] = () => {
                                this.forceUpdate();
                            };
                            if (selectedSpCategoryFieldValue) {
                                let options = getSrvcCategoryFieldOptions(
                                    this.state.viewData?.sp_config_data[0]
                                        ?.settings_data,
                                    ConfigHelpers.isServiceProvider()
                                );
                                spcustomFields.splice(index + 1, 0, {
                                    key: `sp_skill_level_fr_${selectedSpCategoryFieldValue}`,
                                    label: `Skill level for ${getLabel(selectedSpCategoryFieldValue, options)}`,
                                    widget: 'select',
                                    required: true,
                                    options: getSkillLevelOptions(),
                                });
                            }
                        }
                        singleSpCustFields['widgetProps'] = {
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    }
                }
                const authorizedSpFrThisField =
                    fieldVsAuthorityMapping[singleSpCustFields.key];
                if (
                    Array.isArray(authorizedSpFrThisField) &&
                    authorizedSpFrThisField.length > 0 &&
                    !authorizedSpFrThisField.includes(
                        ConfigHelpers.getUserUUID()
                    )
                ) {
                    singleSpCustFields['widgetProps'] = {
                        ...(singleSpCustFields['widgetProps'] || {}),
                        disabled: true,
                    };
                }
            });
        }
        // console.log("getCustomFieldsFrmConfig spcustomFields",spcustomFields);
        const authoritySelectorMetaAccTORestrictManualMode =
            this.shouldShowAuthoritySelectorMeta() ? authoritySelectorMeta : [];
        let finalFields = [
            ...customFields,
            ...authoritySelectorMetaAccTORestrictManualMode,
            ...spcustomFields,
        ];
        let hiddenFields = this.props.hidden_fields || [];
        let spHiddenFieldIds = this.props.sp_hidden_fields || [];
        // Concatenate hiddenFields and spHiddenFieldIds
        let combinedHiddenFields = hiddenFields.concat(spHiddenFieldIds);
        const meta = {
            columns: columns,
            formItemLayout: null,
            fields: finalFields.filter(
                (singleField) =>
                    !ConfigHelpers.isFieldHiddenFrUser(
                        singleField.key,
                        combinedHiddenFields
                    )
            ),
        };
        return meta;
    }

    getOpenStatusCollapsableSpecificFields() {
        let srvcConfigData = this.props.srvcConfigData;
        let SelectedCollapsableSpecificFields = [];
        let collapsableSpecificFieldsKey = `collapsable_specific_fields_fr_status_open`;
        let collapsableSpecificFields =
            srvcConfigData?.[collapsableSpecificFieldsKey];
        if (collapsableSpecificFields && collapsableSpecificFields.length > 0) {
            SelectedCollapsableSpecificFields.push(
                ...collapsableSpecificFields
            );
        }
        return SelectedCollapsableSpecificFields;
    }

    getCustomFieldsFrmConfig(isFrmBulkCreation = false) {
        let columns =
            // Check if the screen is in mobile view or the zoom level is 125%
            isMobileView() || isScreenZoomPercentage125()
                ? // If in mobile screen or zoom percentage of the screen is 125, set `columns` to 2
                  2
                : // Otherwise, use the custom column span from configuration, if available
                  this.props.srvcConfigData?.srvc_cust_fields_json_colspan ||
                  // default to 2
                  2;

        let customFields = decodeFieldsMetaFrmJson(
            this.getCustomFieldsJson(),
            columns,
            false,
            true,
            this.formRef
        );
        // console.log("getCustomFieldsFrmConfig customFields",customFields);
        let fieldVsAuthorityMapping = this.getFieldVsAuthorityMapping();

        // console.log('fieldVsAuthorityMapping',fieldVsAuthorityMapping);
        if (customFields) {
            customFields.map((singleCustFields, index) => {
                if (singleCustFields?.widget == 'select') {
                    if (singleCustFields?.widgetProps?.mode == 'multiple') {
                        singleCustFields['widgetProps'] = {
                            mode: 'multiple',
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    } else {
                        if (
                            singleCustFields?.key ==
                            this.props?.srvcConfigData?.srvc_category_field
                        ) {
                            singleCustFields['onChange'] = () => {
                                this.forceUpdate();
                            };
                            const selectedCategoryFieldValue =
                                this.formRef?.current?.getFieldValue(
                                    `${this.props?.srvcConfigData?.srvc_category_field}`
                                );
                            if (selectedCategoryFieldValue) {
                                let options = getSrvcCategoryFieldOptions(
                                    this?.props?.srvcConfigData,
                                    false
                                );
                                customFields.splice(index + 1, 0, {
                                    key: `skill_level_fr_${selectedCategoryFieldValue}`,
                                    label: `Skill level for ${getLabel(selectedCategoryFieldValue, options)}`,
                                    widget: 'select',
                                    required: true,
                                    options: getSkillLevelOptions(),
                                });
                            }
                        }
                        singleCustFields['widgetProps'] = {
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    }
                } else {
                    if (
                        singleCustFields?.key ==
                            'ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1' &&
                        this.isSPFieldsDynamicForm() &&
                        ConfigHelpers.isServiceProvider() &&
                        this.state.TMS250303062278
                    ) {
                        singleCustFields['widget'] = RemoteSourceSelect;
                        singleCustFields['widgetProps'] = {
                            mode: 'single',
                            url: '/searcher',
                            placeholder: 'Start typing..',
                            params: {
                                fn: 'getSearchOldSiteRefResults',
                            },
                            widgetProps: {
                                mode: 'single',
                                labelInValue: false,
                                showSearch: true,
                                allowClear: true,
                                style: {
                                    width: '100%',
                                },
                            },
                            onChange: (value, option) => {
                                if (!value) {
                                    touchFieldsKeys = [
                                        ...touchFieldsKeys,
                                        'ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1',
                                    ];
                                }
                            },
                        };
                    }
                }
                const authorizedUsersFrThisField =
                    fieldVsAuthorityMapping[singleCustFields.key];
                if (
                    Array.isArray(authorizedUsersFrThisField) &&
                    authorizedUsersFrThisField.length > 0 &&
                    !authorizedUsersFrThisField.includes(
                        ConfigHelpers.getUserUUID()
                    )
                ) {
                    singleCustFields['widgetProps'] = {
                        ...(singleCustFields['widgetProps'] || {}),
                        disabled: true,
                    };
                }
            });
        }
        let srvcPrvdrAuthority = [];
        if (
            ConfigHelpers.isSrvcPrvdrBySrvcReqId(this.state.viewData?.form_data)
        ) {
            srvcPrvdrAuthority = [...(this.getSPAuthoritySelectorMeta() || [])];
        }
        let brandAuthority = [];
        if (
            (!ConfigHelpers.isSrvcPrvdrBySrvcReqId(
                this.state.viewData?.form_data
            ) ||
                !this.isCustomerRequests()) &&
            (isFrmBulkCreation ||
                !this.props.srvcConfigData?.[
                    'hide_authority_selection_from_specific_details'
                ])
        ) {
            brandAuthority = [...(this.getAuthoritySelectorMeta() || [])];
        }
        const authoritySelectorMeta = [
            ...brandAuthority,
            ...srvcPrvdrAuthority,
        ];
        authoritySelectorMeta.forEach((singleMeta) => {
            singleMeta['widgetProps'] = {
                showSearch: true,
                optionFilterProp: 'children',
                ...(singleMeta.widgetProps || {}),
            };
        });
        const selectedSpCategoryFieldValue =
            this.formRef?.current?.getFieldValue(
                `${this.state.viewData?.sp_config_data[0]?.settings_data.srvc_prvdr_category_field}`
            );
        const spcustomFields = this.state.isManipulatedDynamicSpFieldMeta
            ? []
            : [...(this.getSpCustomFieldsMeta() || [])];
        if (spcustomFields) {
            spcustomFields.map((singleSpCustFields, index) => {
                if (singleSpCustFields?.widget == 'select') {
                    if (singleSpCustFields?.widgetProps?.mode == 'multiple') {
                        singleSpCustFields['widgetProps'] = {
                            mode: 'multiple',
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    } else {
                        if (
                            singleSpCustFields?.key ==
                            this.state.viewData?.sp_config_data[0]
                                ?.settings_data.srvc_prvdr_category_field
                        ) {
                            singleSpCustFields['onChange'] = () => {
                                this.forceUpdate();
                            };
                            if (selectedSpCategoryFieldValue) {
                                let options = getSrvcCategoryFieldOptions(
                                    this.state.viewData?.sp_config_data[0]
                                        ?.settings_data,
                                    ConfigHelpers.isServiceProvider()
                                );
                                spcustomFields.splice(index + 1, 0, {
                                    key: `sp_skill_level_fr_${selectedSpCategoryFieldValue}`,
                                    label: `Skill level for ${getLabel(selectedSpCategoryFieldValue, options)}`,
                                    widget: 'select',
                                    required: true,
                                    options: getSkillLevelOptions(),
                                });
                            }
                        }
                        singleSpCustFields['widgetProps'] = {
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        };
                    }
                }
                const authorizedSpFrThisField =
                    fieldVsAuthorityMapping[singleSpCustFields.key];
                if (
                    Array.isArray(authorizedSpFrThisField) &&
                    authorizedSpFrThisField.length > 0 &&
                    !authorizedSpFrThisField.includes(
                        ConfigHelpers.getUserUUID()
                    )
                ) {
                    singleSpCustFields['widgetProps'] = {
                        ...(singleSpCustFields['widgetProps'] || {}),
                        disabled: true,
                    };
                }
            });
        }
        // console.log("getCustomFieldsFrmConfig spcustomFields",spcustomFields);
        const authoritySelectorMetaAccTORestrictManualMode =
            this.shouldShowAuthoritySelectorMeta() ? authoritySelectorMeta : [];
        let finalFields = [
            ...customFields,
            ...authoritySelectorMetaAccTORestrictManualMode,
            ...spcustomFields,
        ];
        let hiddenFields = this.props.hidden_fields || [];
        let spHiddenFieldIds = this.props.sp_hidden_fields || [];
        // Concatenate hiddenFields and spHiddenFieldIds
        let combinedHiddenFields = hiddenFields.concat(spHiddenFieldIds);
        const meta = {
            columns: columns,
            formItemLayout: null,
            fields: finalFields.filter(
                (singleField) =>
                    !ConfigHelpers.isFieldHiddenFrUser(
                        singleField.key,
                        combinedHiddenFields
                    )
            ),
        };
        return meta;
    }
    getFilteredCustomFields(onlyAuthoritiesFieldsMeta = false) {
        let customFields = this.getCustomFieldsFrmConfig();
        let fields = customFields?.fields;
        let columns = customFields?.columns;

        if (onlyAuthoritiesFieldsMeta) {
            fields = fields.filter((item) => item.key.includes('authority_'));
        } else {
            fields = fields.filter((item) => !item.key.includes('authority_'));
        }
        return { fields, columns };
    }

    getAuthoritiesCustomFieldsFrmConfig() {
        let customFields = this.getFilteredCustomFields(true);
        const meta = {
            columns: customFields?.columns,
            formItemLayout: null,
            fields: customFields?.fields || [],
        };
        return this.shouldShowAuthoritySelectorMeta() ? meta : [];
    }

    getSpecificDetailsFieldsFrmConfig() {
        let customFields = this.getFilteredCustomFields();
        let fields = customFields?.fields;
        let columns = customFields?.columns;
        console.log(
            'getSpecificDetailsFieldsFrmConfig customFields',
            customFields
        );
        if (fields && fields.length > 0) {
            //Remove selected Collapsable Specific Fields from fields
            if (this.props.isCustAcces || !this.isOrgSrvcPrvdr()) {
                let allCollapsableSpecificFields =
                    this.getSelectedCollapsableSpecificFields();
                if (
                    allCollapsableSpecificFields &&
                    allCollapsableSpecificFields.length > 0
                ) {
                    //For BarcodeScanner
                    let barcode_scanner_prefix_key;
                    let barcode_scanner_key = fields?.filter(
                        (singleItem) => singleItem?.type == 'Barcode_scanner'
                    )?.[0]?.key;
                    if (
                        allCollapsableSpecificFields?.includes(
                            barcode_scanner_key
                        )
                    ) {
                        //make a barcode scanner prefix key and remove BarcodeScanner from custom fields
                        barcode_scanner_prefix_key = `${barcode_scanner_key}_scanner`;
                    }
                    fields = fields.filter(
                        (item) =>
                            !allCollapsableSpecificFields?.includes(item.key) &&
                            item.key != barcode_scanner_prefix_key
                    );
                }
            }
        }

        let disableFieldsFrPrvdr =
            this.props.srvcConfigData
                ?.make_custom_fields_non_editable_from_provider;
        let disableFieldsArr =
            this.props.srvcConfigData
                ?.select_custom_fields_to_be_made_non_editable || [];

        if (disableFieldsFrPrvdr && ConfigHelpers.isServiceProvider()) {
            fields.forEach((singleEntry) => {
                // Check if the key is in the disableFieldsArr
                if (disableFieldsArr.includes(singleEntry.key)) {
                    // Add the "disabled" property to singleEntry
                    singleEntry.disabled = true;
                }
            });
        }
        const meta = {
            columns: columns,
            formItemLayout: null,
            fields: fields || [],
        };
        console.log('getSpecificDetailsFieldsFrmConfig', meta);
        return meta;
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                    } else if (
                        this.props.showEditor &&
                        this.state.editMode == undefined
                    ) {
                        // loading dynamic form lambda when about to create new service req
                        if (this.isDynamicForm()) {
                            this.onFormValueChanged({}, {});
                        }
                        // Fetch capacity data when opening modal for new request
                        this.fetchCapacityData();
                    }
                }
            );
        } else {
            if (this.state.refreshOnUpdate) {
                this.setState({ refreshOnUpdate: false }, this.initViewData());
            } else {
                if (this.shouldUpdateBillingData()) {
                    this.setState(
                        { updateBillingData: false },
                        this.submitForm(
                            this.formRef?.current?.getFieldValue(),
                            false,
                            true
                        )
                    );
                }
            }
        }
    }

    shouldUpdateBillingData = () => {
        return (
            this.formRef?.current &&
            this.state.updateBillingData &&
            this.formRef?.current?.getFieldValue()?.sp_final_amount &&
            this.state?.viewData?.form_data?.form_data &&
            this.formRef?.current?.getFieldValue()?.sp_final_amount !=
                this.state?.viewData?.form_data?.form_data?.sp_final_amount
        );
    };
    handleOk = () => {
        this.setState({ visible: false, isFormSubmitting: false });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({ refreshOnUpdate: true, ...this.initState });
    }

    tellParentToRefreshList(entry_id) {
        // console.log("Trying to to tell parent to refresh list");
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    refreshSelf(entry_id) {
        this.setState({ refreshOnUpdate: true });
    }

    onDayWiseBillingChangeSubmitForm(entry_id) {
        let spKeyPrefix = ConfigHelpers.isServiceProvider() ? 'sp_' : '';
        this.formRef.current.setFieldsValue({
            [`${spKeyPrefix + 'final_amount'}`]: undefined,
        });
        this.setState({ refreshOnUpdate: true, updateBillingData: true });
    }

    setCalnedarData(value) {
        this.setState({ calendarData: value });
    }

    handleCancel = () => {
        this.setState({ visible: false });
        this.updateClosureToParent();

        // Check if the current URL contains the query parameter 'showItemEditor=true' then redirect to /services/srvc_id
        const urlParams = new URLSearchParams(window.location.search);
        const showItemEditor = urlParams.get('showItemEditor');
        if (showItemEditor === 'true') {
            window.location.replace(`/services/` + this.props.srvc_id);
        }
    };

    getLineItemsWithRevisions() {
        const newLineItemsData = { ...this.state.lineItemsData };
        if (newLineItemsData.revisions == undefined) {
            newLineItemsData['revisions'] = [];
        }
        // create a revision
        const newRevisions = {
            c_by: ConfigHelpers.getUserUUID(),
            c_name: ConfigHelpers.getUserFirstName(),
            c_time: moment.utc(),
            ...newLineItemsData,
            form_data: newLineItemsData.form_data,
        };
        newLineItemsData['revisions'] = [
            ...newLineItemsData['revisions'],
            newRevisions,
        ];
        return newLineItemsData;
    }

    getSPLineItemsWithRevisions() {
        const newLineItemsData = { ...this.state.splineItemsData };
        if (newLineItemsData.revisions == undefined) {
            newLineItemsData['revisions'] = [];
        }
        // create a revision for service provider
        const newRevisions = {
            c_by: ConfigHelpers.getUserUUID(),
            c_name: ConfigHelpers.getUserFirstName(),
            c_time: moment.utc(),
            ...newLineItemsData,
            form_data: newLineItemsData.form_data,
        };
        newLineItemsData['revisions'] = [
            ...newLineItemsData['revisions'],
            newRevisions,
        ];
        return newLineItemsData;
    }

    onFinishFailed = (errorInfo) => {
        notification.error({
            message: 'Error',
            description: errorInfo.errorFields
                .map((err) => err.errors)
                .join(', '),
            placement: 'bottomLeft',
        });
    };

    hasAnyBarcodeValueChanged = (form_data, fieldMeta, prefill_data) => {
        let returnFields = {};

        fieldMeta.forEach((singleMeta) => {
            if (
                form_data?.[singleMeta.key] != prefill_data?.[singleMeta.key] &&
                singleMeta?.type == 'Barcode_scanner'
            ) {
                returnFields[singleMeta.key] = form_data[singleMeta.key];
            }
        });
        return returnFields;
    };

    getOriginalMetaFrDynamicForm() {
        let custFieldJson = this.props.srvcConfigData?.srvc_cust_fields_json;
        if (custFieldJson) {
            custFieldJson = decodeFieldsMetaFrmJson(
                custFieldJson,
                undefined,
                true
            );
        }
        return custFieldJson;
    }

    getOriginalSPMetaFrDynamicForm() {
        let spCustFieldJson =
            this.state.viewData?.sp_config_data[0]?.settings_data
                ?.sp_cust_fields_json;
        if (spCustFieldJson) {
            spCustFieldJson = decodeFieldsMetaFrmJson(spCustFieldJson);
        }
        return spCustFieldJson;
    }

    setIsExecutingDynamicFormLogic(value) {
        this.setState({ isExecutingDynamicFormLogic: value });
    }

    executeDynamicFormLogic = (changedValues, allValues, isDynamicSPFields) => {
        // Write a API call to TMS here
        this.setIsExecutingDynamicFormLogic(true);
        // message.warning(<Spin/>);// To be set by jainish

        allValues['attachments'] = this.state.filesBySection;
        allValues['mic_files'] = this.state.micRecordingsBySection;
        allValues['camera_files'] = this.state.cameraRecordingsBySection;

        let params = {};

        if (isDynamicSPFields) {
            params = {
                changedValues,
                allValues,
                meta: [
                    ...(this.getOriginalMetaFrDynamicForm() || []),
                    ...(this.getOriginalSPMetaFrDynamicForm() || []),
                ],
                currentMeta: this.state.manipulatedDynamicMeta,
                arn: this.state.viewData?.sp_config_data[0]?.settings_data
                    ?.sp_cust_fields_dynamic_form_lambda_arn,
                isDynamicSPFields,
            };
        } else {
            params = {
                changedValues,
                allValues,
                meta: this.getOriginalMetaFrDynamicForm(),
                currentMeta: this.state.manipulatedDynamicMeta,
            };
        }
        // console.log('executeDynamicFormLogic params',params);
        // params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;
        // params['srvc_req_id'] = this.state?.viewData?.form_data?.srvc_req_id;
        // params['srvc_type_id'] = this.state?.viewData?.form_data?.srvc_type_id;

        http_utils.performPostCall(
            dynamicFormLogicPath +
                '/' +
                this.props.srvcDetails?.srvc_id +
                '/' +
                (this.props.isCustAcces ? '1' : '0') +
                '/' +
                (this.props.editorItem?.id || 0),
            params,
            (resp) => {
                if (!this.state.visible) {
                    return;
                }
                // console.log('executeDynamicFormLogic resp.data', resp.data);
                const {
                    meta,
                    allValues,
                    changedValues,
                    manipulatedFieldValues,
                } = resp.data.data;
                this.setState(
                    {
                        manipulatedDynamicMeta: meta,
                        isManipulatedDynamicSpFieldMeta: isDynamicSPFields,
                    },
                    () => {
                        // For files,mic,camera
                        this.initConfigData(
                            { data: this.state.viewData },
                            () => {
                                if (this.formRef?.current) {
                                    this.formRef.current.setFieldsValue(
                                        manipulatedFieldValues
                                    );
                                    const manipulatedFieldKeys =
                                        Object.keys(manipulatedFieldValues) ||
                                        [];
                                    const changedFieldsKeys =
                                        Object.keys(changedValues) || [];
                                    touchFieldsKeys = [
                                        ...touchFieldsKeys,
                                        ...manipulatedFieldKeys,
                                        ...changedFieldsKeys,
                                    ];
                                }
                                this.setIsExecutingDynamicFormLogic(false);
                            }
                        );
                    }
                );
            },
            (error) => {
                console.log(
                    'Error in onFormValueChange API call',
                    http_utils.decodeErrorToMessage(error)
                );
                message.error('Auto form change not working!, Contact admin ');
                this.setIsExecutingDynamicFormLogic(false);
            }
        );
    };

    onFormValueChanged = (
        changedValues,
        allValues,
        isDynamicSPFields = false
    ) => {
        if (debouncer) {
            clearTimeout(debouncer);
        }
        debouncer = setTimeout(() => {
            if (this.state.visible) {
                this.executeDynamicFormLogic(
                    changedValues,
                    allValues,
                    isDynamicSPFields
                );
            }
        }, 750);
    };

    submitForm = (data, isComment = false, keepOpenParentModel = false) => {
        this.setState({ isFormSubmitting: !keepOpenParentModel });
        let touchedFields = {};
        if (!isComment) {
            touchedFields = getTouchedFieldsValueInForm(
                data,
                this.formRef?.current,
                touchFieldsKeys
            );
            touchedFields = {
                ...touchedFields,
                ...this.hasAnyBarcodeValueChanged(
                    data,
                    this.getCustomFieldsFrmConfig().fields,
                    this.state.viewData?.form_data?.form_data
                ),
            };
            touchedFields['update_for_comment'] = false;
            if (this.state.editMode) {
                // compare files with prefill data
                let isAnyFileChanged = hasAnyFileChanged(
                    this.state.filesBySection,
                    this.state.viewData?.form_data?.form_data.attachments
                );
                if (isAnyFileChanged) {
                    touchedFields['attachments'] = this.state.filesBySection;
                    touchedFields['lastChangedFileSectionIds'] =
                        this.state.lastChangedFileSectionIds;
                }
                // compare files with prefill data
                let isAnyMicFileChanged = hasAnyFileChanged(
                    this.state.micRecordingsBySection,
                    this.state.viewData?.form_data?.form_data.mic_files
                );
                if (isAnyMicFileChanged) {
                    touchedFields['mic_files'] =
                        this.state.micRecordingsBySection;
                }
                //compare camera files
                let isAnyCameraFileChanged = hasAnyFileChanged(
                    this.state.cameraRecordingsBySection,
                    this.state.viewData?.form_data?.form_data.camera_files
                );
                if (isAnyCameraFileChanged) {
                    touchedFields['camera_files'] =
                        this.state.cameraRecordingsBySection;
                }
                if (this.state.lineItemsData) {
                    touchedFields['line_items'] =
                        this.getLineItemsWithRevisions();
                }
                if (this.state.splineItemsData) {
                    touchedFields['sp_line_items'] =
                        this.getSPLineItemsWithRevisions();
                }
                if (this.state.spPayoutsData) {
                    touchedFields['sp_payouts_data'] = this.state.spPayoutsData;
                }
                if (this.state.spDeductionData) {
                    touchedFields['sp_deduction_data'] =
                        this.state.spDeductionData;
                }
                if (this.state.additionalBillingItem) {
                    touchedFields['additional_billing_items'] =
                        this.state.additionalBillingItem;
                }
                if (this.state.finalBillingItem) {
                    touchedFields['final_billing_items'] =
                        this.state.finalBillingItem;
                }
                if (this.state.spAdditionalBillingItem) {
                    touchedFields['sp_additional_billing_items'] =
                        this.state.spAdditionalBillingItem;
                }
                if (this.state.spFinalBillingItem) {
                    touchedFields['sp_final_billing_items'] =
                        this.state.spFinalBillingItem;
                }
                if (
                    this.props.srvcConfigData?.[
                        'hide_authority_selection_from_specific_details'
                    ]
                ) {
                    touchedFields = {
                        ...touchedFields,
                        ...this.state.authoritySelectorFieldVsValue,
                    };
                }
            } else {
                data['attachments'] = this.state.filesBySection;
                data['lastChangedFileSectionIds'] =
                    this.state.lastChangedFileSectionIds;
                data['mic_files'] = this.state.micRecordingsBySection;
                data['camera_files'] = this.state.cameraRecordingsBySection;
                if (this.state.lineItemsData) {
                    data['line_items'] = this.state.lineItemsData;
                }
                if (this.state.splineItemsData) {
                    data['sp_line_items'] = this.state.splineItemsData;
                }
                if (this.state.spPayoutsData) {
                    data['sp_payouts_data'] = this.state.spPayoutsData;
                }
                if (this.state.spDeductionData) {
                    data['sp_deduction_data'] = this.state.spDeductionData;
                }
                if (this.state.additionalBillingItem) {
                    data['additional_billing_items'] =
                        this.state.additionalBillingItem;
                }
                if (this.state.finalBillingItem) {
                    data['final_billing_items'] = this.state.finalBillingItem;
                }
                if (this.state.spAdditionalBillingItem) {
                    data['sp_additional_billing_items'] =
                        this.state.spAdditionalBillingItem;
                }
                if (this.state.spFinalBillingItem) {
                    data['sp_final_billing_items'] =
                        this.state.spFinalBillingItem;
                }
                if (
                    this.props.srvcConfigData?.[
                        'hide_authority_selection_from_specific_details'
                    ]
                ) {
                    touchedFields = {
                        ...touchedFields,
                        ...this.state.authoritySelectorFieldVsValue,
                    };
                }
            }
        } else {
            touchedFields = data;
            touchedFields['update_for_comment'] = true;
        }

        var params = this.state.editMode ? touchedFields : data;
        // var params = data;

        if (Object.keys(params).length == 0) {
            this.setState(
                { isFormSubmitting: false },
                message.info('No change in form')
            );
            // Nothing to submit
            return;
        }
        params['host_d'] = window.location.host;

        const onComplete = (resp) => {
            if (touchedFields?.send_for_billing) {
                this.initViewData();
            }
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: keepOpenParentModel,
                lastChangedFileSectionIds: [],
            });
            this.tellParentToRefreshList(resp.entry_id);
            if (!keepOpenParentModel) {
                this.updateClosureToParent();
            }
            touchFieldsKeys = [];
        };
        const onError = (error) => {
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        if (this.state.editMode) {
            http_utils.performPutCall(
                this.submitUrl + '/' + this.props.editorItem.id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(
                this.submitUrl,
                params,
                onComplete,
                onError
            );
        }
    };

    getAllFieldsMeta() {
        const meta = [
            ...this.getCustomFieldsFrmConfig().fields,
            ...this.getRequestInfoMeta().fields,
            ...this.getAddressInfoMeta().fields,
            ...this.getCustomerInfoMeta().fields,
            ...getCommentstInfoMeta().fields,
            ...getApprovalReqStatusChangeInfoMeta().fields,
            ...getAutoAuthoritiesRoleInfoMeta().fields,
            ...getServiceProviderMeta().fields,
        ];
        return meta;
    }

    refreshForm() {
        this.setState({ render_helper: !this.state.render_helper });
    }

    isSrvcReqDateMandatoryWhileCreation() {
        return (
            this.props?.srvcConfigData
                ?.request_service_date_mandatory_while_request_creation || false
        );
    }

    getRequestInfoMeta() {
        let srvcTypeNature = this.props.srvcConfigData?.srvc_type_nature;
        const startOfDay =
            this.props.srvcConfigData?.sbtsk_time_slot_lower_limit || '05:00AM';
        const endOfDay =
            this.props.srvcConfigData?.sbtsk_time_slot_upper_limit || '11:45PM';
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'request_description',
                    colSpan: 4,
                    label: 'Description',
                    widget: 'textarea',
                    required: true,
                },
                {
                    key: 'request_req_date',
                    label: 'Req. Service Date',
                    colSpan: 2,
                    widgetProps: {
                        disabledDate: (current) =>
                            current && current > moment().add(2, 'month'),
                        style: { width: '100%' },
                        onChange: (value, dateString) => {
                            this.formRef.current.setFieldsValue({
                                request_req_date: moment.utc(dateString),
                            });
                        },
                    },
                    widget: 'date-picker',
                    required: this.isSrvcReqDateMandatoryWhileCreation(),
                },
                ...getSbtskTimeFieldsMetaFrReqCreation(
                    startOfDay,
                    endOfDay,
                    srvcTypeNature,
                    this.formRef,
                    this.state.viewData,
                    () => {
                        this.refreshForm();
                    }
                ),
                {
                    key: 'request_priority',
                    label: 'Priority',
                    colSpan: 2,
                    widget: 'select',
                    options: priorities, // to be loaded from API
                    required: true,
                    widgetProps: {
                        onChange: (value) => {
                            // console.log('Priority - ',value);
                        },
                    },
                },
                {
                    key: 'request_labels',
                    label: 'Labels',
                    colSpan: 2,
                    widget: RemoteSourceSelect,
                    options: tagsProto.suggestions,
                    renderView: (values) => (
                        <div>
                            {typeof values.map == 'function' &&
                                values.map((value) => value.label + ',')}
                        </div>
                    ),
                    widgetProps: {
                        mode: 'tags',
                        url: '/searcher',
                        placeholder: 'Start typing..',
                        params: {
                            srvc_type_id: this.props.srvcDetails?.srvc_id,
                            fn: 'getServiceRequestLabels',
                        },
                        widgetProps: { mode: 'tags', style: { width: '100%' } },
                        onChange: (value) => {
                            // this.formRef.current.setFieldsValue({
                            //     request_labels: value
                            // })
                        },
                    },
                },
                {
                    key: 'request_cc_users',
                    label: 'CC users',
                    widget: UserSelectorWidget,
                    widgetProps: {
                        mode: 'multiple',
                        // onChange : value => {
                        //     console.log('CC users value - ',value);
                        // }
                    },
                    renderView: (values) => (
                        <div>
                            {typeof values.map == 'function' &&
                                values.map((value) => value.label + ',')}
                        </div>
                    ),
                    colSpan: 2,
                },
                {
                    key: 'creation_date',
                    label: 'Creation Date',
                    colSpan: 2,
                    tooltip:
                        'You can specify a past creation date, incase you are creating entry on a different day. You can ignore this if you are creating the request on same day',
                    widgetProps: {
                        disabledDate: (current) =>
                            current && current > moment().endOf('day'),
                        style: { width: '100%' },
                        onChange: (value, dateString) => {
                            this.formRef.current.setFieldsValue({
                                creation_date: moment.utc(dateString),
                            });
                        },
                    },
                    widget: 'date-picker',
                    rules: [
                        {
                            validator: (rule, value) => {
                                if (value && value.isAfter(moment(), 'day')) {
                                    return Promise.reject(
                                        'Future Creation Date are not allowed'
                                    );
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                {
                    key: 'initial_status',
                    label: 'Initial Status (Optional)',
                    colSpan: 2,
                    widget: 'select',
                    tooltip:
                        'Specify starting status so that the request will be auto moved from open status to the specified status. Please keep in mind that notifications to customer will be triggered(if any) as per configuration for the specified status',
                    options: this.state.viewData?.statuses,
                },
                // {
                //     key: 'request_cc_users',
                //     label:'CC users',
                //     colSpan:2,
                //     widget:'select',
                //     options: tagsProto.suggestions,// to be loaded from API
                //     widgetProps:{
                //         mode:"multiple",
                //         onChange : value => {
                //             console.log('CC users value - ',value);
                //         }
                //     },
                // }
            ],
        };
        return meta;
    }

    forceUpdateFn() {
        this.forceUpdate();
    }

    getRefreshLocGrps() {
        const cust_pincode =
            this.state.viewData?.form_data?.form_data?.cust_pincode;
        if (this.state.editMode && cust_pincode) {
            return (
                <RefreshLocationGrp
                    srvc_req_id={this.props?.editorItem?.id}
                    onChange={(locGrpsName) => {
                        this.setState({ locGrpsName: locGrpsName });
                    }}
                />
            );
        }
    }
    getLocationGrpsName() {
        //If brand login then show only brand location groups name.
        //If prvdr than prvdr location groups.
        //If inside customer access then show both loc grps.
        let brand_location_groups =
            this.props.editorItem?.brand_location_groups;
        let prvdr_location_groups =
            this.props.editorItem?.prvdr_location_groups;

        let brand_tag_color = 'green';
        let prvdr_tag_color = 'green';
        if (this.state.locGrpsName) {
            brand_location_groups =
                this.state.locGrpsName?.brand_location_groups;
            prvdr_location_groups =
                this.state.locGrpsName?.prvdr_location_groups;
            if (
                this.state.locGrpsName?.brand_location_groups_status ==
                'not_found'
            ) {
                brand_tag_color = 'red';
            }
            if (
                this.state.locGrpsName?.prvdr_location_groups_status ==
                'not_found'
            ) {
                prvdr_tag_color = 'red';
            }
        }
        if (this.isOrgSrvcPrvdr() && this.props.isCustAcces) {
            return (
                <>
                    {brand_location_groups ? (
                        <p className="gx-mb-auto">
                            Brand
                            <Tooltip
                                placement="topLeft"
                                title="Address is matched to this brand location groups."
                            >
                                <QuestionCircleOutlined className="gx-mr-1 gx-ml-1" />
                            </Tooltip>
                            <Tag
                                color={brand_tag_color}
                                className="break_space"
                            >
                                {' '}
                                {brand_location_groups}{' '}
                            </Tag>
                        </p>
                    ) : null}
                    {prvdr_location_groups ? (
                        <p className="gx-mb-auto">
                            Provider
                            <Tooltip
                                placement="topLeft"
                                title="Address is matched to this service provider location groups."
                            >
                                <QuestionCircleOutlined className="gx-mr-1 gx-ml-1" />
                            </Tooltip>
                            <Tag
                                color={prvdr_tag_color}
                                className="break_space"
                            >
                                {' '}
                                {prvdr_location_groups}{' '}
                            </Tag>
                        </p>
                    ) : null}
                </>
            );
        } else if (this.isOrgSrvcPrvdr()) {
            return prvdr_location_groups ? (
                this.state.selectedCurrentTab === 'profitLossTab' ? (
                    <p>
                        {prvdr_location_groups ===
                        'No matching location group found for the provided pincode'
                            ? '-'
                            : prvdr_location_groups}
                    </p>
                ) : (
                    <Tooltip
                        placement="topLeft"
                        title="Matching location groups"
                    >
                        <p className="gx-mb-auto">
                            <Tag
                                color={prvdr_tag_color}
                                className="break_space"
                            >
                                {prvdr_location_groups}
                            </Tag>
                        </p>
                    </Tooltip>
                )
            ) : null;
        } else {
            return brand_location_groups ? (
                <Tooltip placement="topLeft" title="Matching location groups">
                    <p className="gx-mb-auto">
                        {' '}
                        <Tag color={brand_tag_color} className="break_space">
                            {' '}
                            {brand_location_groups}{' '}
                        </Tag>{' '}
                    </p>
                </Tooltip>
            ) : null;
        }
    }

    getAddressInfoMeta() {
        //Show location groups name
        let location_grps_name = [];
        let locGrpsName = this.getLocationGrpsName();
        const is_pincode_mandatory =
            this.props?.srvcConfigData?.srvc_req_cust_pincode_mandatory;
        if (locGrpsName) {
            location_grps_name.push({
                colSpan: 4,
                render() {
                    return locGrpsName;
                },
            });
        }
        let refresh_loc_grp_btn = [];
        refresh_loc_grp_btn.push({
            render: () => {
                return (
                    <div className="gx-text-right">
                        {this.getRefreshLocGrps()}
                    </div>
                );
            },
        });

        // Get the appropriate address fields based on feature flag
        const addressFields = this.state?.TMS250312325161
            ? getAddressFieldsMetaTMS250312325161(
                  this.formRef,
                  () => {
                      this.forceUpdate();
                  },
                  this.props.srvcConfigData
                      ?.select_mandatory_address_fields_for_a_request || [],
                  this.props.orgSettingsData,
                  this.state.editMode,
                  this.props?.editorItem,
                  (srvc_req_id) => {
                      this.refreshSelf(srvc_req_id);
                  },
                  this.state.viewData?.form_data?.form_data?.cust_pincode
              )
            : getAddressFieldsMeta(
                  this.formRef,
                  () => {
                      this.forceUpdate();
                  },
                  is_pincode_mandatory,
                  this.props.orgSettingsData,
                  this.state.editMode,
                  this.props?.editorItem,
                  (srvc_req_id) => {
                      this.refreshSelf(srvc_req_id);
                  },
                  this.state.viewData?.form_data?.form_data?.cust_pincode
              );

        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'address',
                    colSpan: 4,
                    render() {
                        return (
                            <fieldset>
                                <legend>
                                    <b>Address</b>
                                </legend>
                            </fieldset>
                        );
                    },
                },
                ...refresh_loc_grp_btn,
                ...location_grps_name,
                ...addressFields,
            ],
        };
        return meta;
    }

    handleCustMobileChanged = debounce((phnNo) => {
        if (
            phnNo &&
            phnNo.length == this.props.orgSettingsData?.mobile_digit &&
            !this.state.isLoadingCustomerDetails
        ) {
            // The mobile number is entered completely
            // message.info(phnNo);
            let prevAutoFillId = this.state.autoFillCustId;
            this.setState(
                { isLoadingCustomerDetails: true, autoFillCustId: '' },
                () => {
                    getCustomerDetailsFrmApi(phnNo, (customerDetails) => {
                        let customerId = '';
                        if (customerDetails) {
                            customerId = customerDetails[0].value;
                        }
                        this.setState(
                            {
                                isLoadingCustomerDetails: false,
                                autoFillCustId: customerId,
                                custMobNoFrHis: phnNo,
                            },
                            () => {
                                if (customerDetails) {
                                    // Customer details found
                                    this.updateCustomerDetailsIntoForm(
                                        customerDetails
                                    );
                                } else if (prevAutoFillId != '') {
                                    // the form was previously autofilled
                                    // but now it has a new customer
                                    // so we need to clear the form
                                    // this.clearCustomerDetailsInForm();
                                }
                                this.forceUpdate();
                            }
                        );
                    });
                }
            );
        }
    }, 300);

    clearCustomerDetailsInForm() {
        let customerFields = this.getCustomerInfoMeta().fields.map(
            (singleFieldMeta) => singleFieldMeta.key
        );
        let addressFields = this.getAddressInfoMeta().fields.map(
            (singleFieldMeta) => singleFieldMeta.key
        );
        let allFields = [...customerFields, ...addressFields];
        // do not reset cust_mobile
        allFields = allFields.filter(
            (singleFieldKey) => singleFieldKey != 'cust_mobile'
        );
        this.formRef.current.resetFields(allFields);
    }

    appCall = () => {
        var params = {};
        params['srvc_type_id'] = this.props?.editorItem?.srvc_type_id;
        params['srvc_id'] = this.props?.editorItem?.id;

        // Save into local storage
        let key =
            'last_call_consumer_button_click_' + this.props?.editorItem?.id;
        localStorage.setItem(key, Date.now());

        this.setState({ render_helper: !this.state.render_helper });
        const onComplete = (resp) => {
            // console.log("resp here - ",resp);
            if (resp.data == 'max_call_exceeded') {
                this.setState({
                    appCallBtnText: 'Daily call limit exceeded ✋',
                });
            }
        };
        const onError = (error) => {
            this.setState({
                isFormSubmitting: false,
                app_call_error: http_utils.decodeErrorToMessage(error),
            });
        };

        http_utils.performPostCall(
            '/services/app_call',
            params,
            onComplete,
            onError
        );
    };

    updateCustomerDetailsIntoForm(customerDetails) {
        // Need to update this in form
        // message.info(JSON.stringify(customerDetails));
        this.formRef.current.setFieldsValue(customerDetails[0]);
    }

    getCustomerInfoMeta(hideCustMobileFieldFrReqCreation = true) {
        let enteredMobileNumber =
            this.formRef.current?.getFieldValue('cust_mobile');
        if (
            enteredMobileNumber &&
            enteredMobileNumber.length !=
                this.props.orgSettingsData?.mobile_digit
        ) {
            enteredMobileNumber = false;
        }
        const is_not_mandatory_cust_mob =
            this.props.srvcConfigData?.srvc_cust_mobile_not_mandatory;

        // Exotel integration
        let hide_cust_phn_number = false;
        let visible_cust_call_button = false;
        const config_data = this.props.srvcConfigData;
        const org_settings_config_data = this.props.orgSettingsData;
        const countryCode = org_settings_config_data?.country_code;
        const mobileDigit = org_settings_config_data?.mobile_digit;
        if (config_data) {
            // Hide consumer phone number
            let isServiceProvider = ConfigHelpers.isServiceProvider();
            let isUserOnfield = ConfigHelpers.isUserOnfield();
            let srvc_hide_consumer_phonenumber_from_prvdr =
                config_data.srvc_hide_consumer_phonenumber_from_prvdr;
            let srvc_hide_consumer_phonenumber_from_onfield =
                config_data.srvc_hide_consumer_phonenumber_from_onfield;
            let can_see_consumer_phonenumber_number =
                ConfigHelpers.canSeeConsumerPhnNumber();

            if (isServiceProvider) {
                if (
                    srvc_hide_consumer_phonenumber_from_prvdr ||
                    (!can_see_consumer_phonenumber_number &&
                        hideCustMobileFieldFrReqCreation) ||
                    (isUserOnfield &&
                        srvc_hide_consumer_phonenumber_from_onfield)
                ) {
                    hide_cust_phn_number = true;
                }
            } else {
                if (
                    !can_see_consumer_phonenumber_number &&
                    hideCustMobileFieldFrReqCreation
                ) {
                    hide_cust_phn_number = true;
                }
            }
            // Call consumer button
            let enable_consumer_call_button =
                ConfigHelpers.enableCustCallButton();
            if (enable_consumer_call_button) {
                visible_cust_call_button = true;
            }
        }
        let cust_mobile_number = [];
        if (!hide_cust_phn_number) {
            cust_mobile_number.push({
                key: 'cust_mobile',
                label: `Mobile(${countryCode})`,
                required: !is_not_mandatory_cust_mob ? true : false,
                onChange: (e) => {
                    if (!this.state.editMode && !this.props.isCustAcces)
                        this.handleCustMobileChanged(e.target.value);
                },
                widgetProps: {
                    suffix: this.state.isLoadingCustomerDetails ? (
                        <Spin size="small" className="gx-m-0" />
                    ) : (
                        ''
                    ),
                },
                extra:
                    !this.props.isCustAcces && this.state.autoFillCustId ? (
                        <span className="gx-text-grey">
                            (Existing customer)
                        </span>
                    ) : enteredMobileNumber &&
                      !this.state.editMode &&
                      !this.state.isLoadingCustomerDetails ? (
                        <span>
                            (New customer){' '}
                            <a
                                onClick={(e) =>
                                    this.clearCustomerDetailsInForm()
                                }
                            >
                                Reset/Clear fields
                            </a>
                        </span>
                    ) : (
                        ''
                    ),

                rules: [
                    {
                        pattern: new RegExp('^[0-9]*$'),
                        message: 'Incorrect number',
                    },
                    {
                        min: mobileDigit,
                        message: `Mobile (${countryCode}) must be ${mobileDigit} characters.`,
                    },
                    {
                        max: mobileDigit,
                        message: `Mobile (${countryCode}) must be ${mobileDigit} characters.`,
                    },
                ],
            });
        }

        let cust_call_btn = [];
        if (visible_cust_call_button) {
            this.state.editMode &&
                cust_call_btn.push({
                    key: `app_call_button`,
                    render: () => {
                        return (
                            <>
                                <Button
                                    type="primary"
                                    size="small"
                                    className=""
                                    onClick={() => this.appCall()}
                                    disabled={this.getIsCalling()}
                                >
                                    <PhoneOutlined />{' '}
                                    {this.state.appCallBtnText ||
                                        this.getCallBtnText()}
                                </Button>
                                {this.state.app_call_error ? (
                                    <p className="gx-text-red">
                                        {this.state.app_call_error}
                                    </p>
                                ) : null}
                            </>
                        );
                    },
                });
        }

        const customerInfoFields = [
            ...cust_call_btn,
            ...cust_mobile_number,
            {
                key: 'cust_full_name',
                label: 'Name',
                required: true,
                rules: [{ max: 100 }],
            },
            {
                key: 'cust_email',
                label: 'Email',
                rules: [{ type: 'email', max: 100 }],
            },
        ];
        const meta = {
            formItemLayout: null,
            fields: [
                {
                    key: 'customer_info',
                    colSpan: 4,
                    render() {
                        return (
                            <fieldset>
                                <legend>
                                    <b>Customer information</b>
                                </legend>
                            </fieldset>
                        );
                    },
                },
                ...customerInfoFields,
            ],
        };
        return meta;
    }

    onFilesChanged(section, files) {
        let newFilesBySection = this.state.filesBySection;
        newFilesBySection[section] = files;

        let sprcificFilesSection = [...this.state.lastChangedFileSectionIds];
        sprcificFilesSection.push(section);

        this.setState({
            filesBySection: newFilesBySection,
            lastChangedFileSectionIds: sprcificFilesSection,
        });
    }
    onMicFilesChanged(section, files) {
        let newFilesBySection = this.state.micRecordingsBySection;
        newFilesBySection[section] = files;
        this.setState({ micRecordingsBySection: newFilesBySection });
    }
    onCameraFilesChanged(section, files) {
        let newFilesBySection = this.state.cameraRecordingsBySection;
        newFilesBySection[section] = files;
        this.setState({ cameraRecordingsBySection: newFilesBySection });
    }

    onFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({ sectionWiseUploaderReady: newSectionWiseReady });
    }
    onMicFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseMicUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({ sectionWiseMicUploaderReady: newSectionWiseReady });
    }
    onCameraFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseCameraUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({ sectionWiseCameraUploaderReady: newSectionWiseReady });
    }

    getAllFileUploadersReady() {
        let {
            sectionWiseUploaderReady,
            sectionWiseMicUploaderReady,
            sectionWiseCameraUploaderReady,
        } = this.state;
        let notReady = false;
        Object.keys(sectionWiseUploaderReady).map((section) => {
            if (!sectionWiseUploaderReady[section]) {
                notReady = true;
            }
        });
        Object.keys(sectionWiseMicUploaderReady).map((section) => {
            if (!sectionWiseMicUploaderReady[section]) {
                notReady = true;
            }
        });
        Object.keys(sectionWiseCameraUploaderReady).map((section) => {
            if (!sectionWiseCameraUploaderReady[section]) {
                notReady = true;
            }
        });
        return !notReady;
    }

    getFieldsMetaFrBulkUpload() {
        let addressInfoMeta = this.getAddressInfoMeta().fields;
        const keysToExclude = ['address', 'location', 'clear_fields'];
        addressInfoMeta = addressInfoMeta.filter(
            (item) => !keysToExclude.includes(item.key)
        );
        return [
            ...this.getCustomerInfoMeta(false).fields,
            ...addressInfoMeta,
            ...this.getCustomFieldJsonFrBulk(true).fields,
            ...this.getRequestInfoMeta().fields,
        ];
    }

    getCustName() {
        return this.formRef.current?.getFieldValue('cust_full_name');
    }

    setAuthoritiesFields(newRowData) {
        let fieldsVsValue = {};
        Object.keys(newRowData).forEach((singleRoleId) => {
            fieldsVsValue[singleRoleId] = newRowData[singleRoleId];
        });
        this.formRef.current.setFieldsValue(fieldsVsValue);
        // console.log("fieldsVsValue",fieldsVsValue);
        let authorityList = this.getAuthoritySelectorMeta();
        // console.log("authorityList",authorityList);
        let selectedAuthorityFieldVsValues = {};
        authorityList.forEach((singleAuthority) => {
            selectedAuthorityFieldVsValues[singleAuthority.key] =
                newRowData[singleAuthority.key];
        });
        this.setState({
            authoritySelectorFieldVsValue: selectedAuthorityFieldVsValues,
        });
    }

    getAuthoritySelectorMeta() {
        let authorities_owners_list = [];
        if (!ConfigHelpers.isServiceProvider() || !this.isCustomerRequests()) {
            authorities_owners_list =
                this.state.viewData?.role_wise_authorities_users_list;
        } else {
            let srvcConfigData = this.props?.srvcConfigData;
            let authorities_users_list =
                this.state.viewData?.role_wise_authorities_users_list;
            if (authorities_users_list) {
                authorities_users_list.forEach((singleAuthority) => {
                    let srvc_config_authority_key =
                        singleAuthority.role_id +
                        '_enable_cross_visibility_of_authorities';
                    if (srvcConfigData[srvc_config_authority_key]) {
                        authorities_owners_list.push(singleAuthority);
                    }
                });
            }
        }
        return authorities_owners_list;
    }

    shouldShowAuthoritySelectorMeta() {
        return (
            !this.props.srvcConfigData?.restrict_manual_authority_selection ||
            this.props?.editorItem
        );
    }

    getSPAuthoritySelectorMeta(forAuthoritySelector = false) {
        let authorities_owners_list = [];
        if (ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) {
            authorities_owners_list =
                this.state.viewData
                    ?.srvc_prvdr_role_wise_authorities_users_list;
        } else {
            let spAuthoritiesConfigData =
                this.props?.spAuthoritiesConfigData ||
                this.state.viewData?.sp_authorities_config_data;
            let authorities_users_list =
                this.state.viewData
                    ?.srvc_prvdr_role_wise_authorities_users_list;
            if (authorities_users_list) {
                authorities_users_list.forEach((singleAuthority) => {
                    let srvc_config_authority_key =
                        singleAuthority.role_id +
                        '_enable_cross_visibility_of_authorities';
                    if (spAuthoritiesConfigData?.[srvc_config_authority_key]) {
                        authorities_owners_list.push(singleAuthority);
                    }
                });
            }
        }
        if (!forAuthoritySelector) {
            let final_authority_list = [];
            if (authorities_owners_list) {
                authorities_owners_list.forEach((singleAuthority) => {
                    if (singleAuthority.is_loc_grp_filteration_enabled_fr_sp) {
                        let data = [...singleAuthority.options] || [];
                        let optionsData = [
                            {
                                label: 'Matching location group',
                                options: data
                                    .filter(
                                        (singleItem) =>
                                            singleItem.is_loc_grp_matched_with_srvc_req
                                    )
                                    .map((item) => ({
                                        key: `match_${item.value}`,
                                        label: item.label,
                                        value: item.value,
                                    })),
                            },
                            {
                                label: 'All',
                                options: data.map((item) => ({
                                    key: `all_${item.value}`,
                                    label: item.label,
                                    value: item.value,
                                })),
                            },
                        ];

                        final_authority_list.push({
                            ...singleAuthority,
                            widgetProps: {
                                options: optionsData,
                                optionFilterProp: 'label',
                            },
                        });
                    } else {
                        final_authority_list.push(singleAuthority);
                    }
                });
                return final_authority_list;
            }
        }
        return authorities_owners_list;
    }

    getSpCustomFieldsMeta() {
        let sp_cust_fields_json = [];
        let sp_config_data;
        if (ConfigHelpers.isServiceProvider()) {
            sp_config_data =
                this.state.viewData?.form_data?.sp_config_data?.[0]
                    ?.settings_data?.sp_cust_fields_json;
            if (sp_config_data) {
                sp_cust_fields_json = decodeFieldsMetaFrmJson(sp_config_data);
            }
        }
        return sp_cust_fields_json;
    }

    getSpCustomFieldsMetaFrBilling() {
        let sp_cust_fields_json = [];
        let sp_config_data =
            this.state.viewData?.sp_config_data?.[0]?.settings_data
                ?.sp_cust_fields_json;
        if (sp_config_data) {
            sp_cust_fields_json = decodeFieldsMetaFrmJson(sp_config_data);
        }
        return sp_cust_fields_json;
    }

    getLineItemConfig() {
        const lineItemConfig =
            this.props.srvcConfigData?.srvc_type_line_item_config;
        return lineItemConfig ? JSON.parse(lineItemConfig) : {};
    }

    spGetLineItemConfig() {
        if (ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) {
            const lineItemConfig =
                this.state.viewData?.form_data?.sp_config_data?.[0]
                    ?.settings_data?.srvc_type_line_item_config;
            return lineItemConfig ? JSON.parse(lineItemConfig) : {};
        } else {
            const lineItemConfig =
                this.props?.sp_config_data?.settings_data
                    ?.srvc_type_line_item_config;
            return lineItemConfig ? JSON.parse(lineItemConfig) : {};
        }
    }

    getSpPayoutsConfig() {
        if (ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) {
            const spPayoutConfig =
                this.state.viewData?.form_data?.sp_config_data?.[0]
                    ?.settings_data?.srvc_type_sp_payouts_config;
            return spPayoutConfig ? JSON.parse(spPayoutConfig) : {};
        } else {
            const spPayoutConfig =
                this.props?.sp_config_data?.settings_data
                    ?.srvc_type_sp_payouts_config;
            return spPayoutConfig ? JSON.parse(spPayoutConfig) : {};
        }
    }

    getRequestData() {
        return this.state.viewData?.form_data;
    }

    getConfigDataFrSrvcPrvdr() {
        let sp_config_data =
            this.state.viewData?.sp_config_data?.[0]?.settings_data;

        //Get service provider price_config_data for line_item & manday from history table [cl_tx_sp_fields_history]
        if (sp_config_data) {
            let sp_pricing_config_his =
                this.state.viewData?.form_data
                    ?.sp_srvc_type_history_pricing_config;
            if (sp_pricing_config_his && sp_pricing_config_his?.length > 0) {
                let spPriceConfigFrLineItem =
                    sp_pricing_config_his?.[0]
                        ?.srvc_type_pricing_config_for_line_item;
                if (spPriceConfigFrLineItem && spPriceConfigFrLineItem != '') {
                    sp_config_data['srvc_type_pricing_config_for_line_item'] =
                        spPriceConfigFrLineItem;
                } else {
                    //If service type is not configuration from provider side
                    sp_config_data['srvc_type_pricing_config_for_line_item'] =
                        '{}';
                }

                let spPriceConfigFrManday =
                    sp_pricing_config_his?.[0]
                        ?.srvc_type_pricing_config_for_manday;
                if (spPriceConfigFrManday && spPriceConfigFrManday != '') {
                    sp_config_data['srvc_type_pricing_config_for_manday'] =
                        spPriceConfigFrManday;
                } else {
                    sp_config_data['srvc_type_pricing_config_for_manday'] =
                        '{}';
                }
            }
        }
        return sp_config_data; //this.state.viewData?.sp_config_data?.[0]?.settings_data;
    }

    getDeploymentConfigData() {
        return ConfigHelpers.isServiceProvider()
            ? this.state.viewData?.form_data?.sp_config_data?.[0]?.settings_data
            : this.props.srvcConfigData;
    }

    isProjectNature() {
        const configData = this.props.srvcConfigData;
        return configData?.srvc_type_nature == PROJECT_BASED_SERVICE_TYPE;
    }

    noInitialDeployment() {
        return getSrvcReqCalendarData(
            this.state.calendarData,
            ConfigHelpers.isServiceProvider()
        ).length > 0
            ? false
            : true;
    }

    isBillingEnable() {
        const configData = this.props.srvcConfigData;
        return configData?.srvc_type_enable_billing;
    }
    isRatingEnable() {
        const configData = this.props.srvcConfigData;
        return configData?.enable_rating;
    }

    haveAccessFrBilling() {
        let configData;
        if (ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) {
            configData =
                this.state.viewData?.form_data?.sp_config_data?.[0]
                    ?.settings_data;
        } else {
            configData = this.props?.sp_config_data?.settings_data;
        }
        let checkUserSeeBillingTab =
            ConfigHelpers.canUserSeeBillingTab(configData);
        return checkUserSeeBillingTab;
    }

    isPrvdrBillingEnable() {
        let configData;
        if (ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) {
            configData =
                this.state.viewData?.form_data?.sp_config_data?.[0]
                    ?.settings_data;
        } else {
            configData = this.props?.sp_config_data?.settings_data;
        }
        return configData?.srvc_type_enable_billing;
    }
    isSpRatingsEnable() {
        let configData;
        if (ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) {
            configData =
                this.state.viewData?.form_data?.sp_config_data?.[0]
                    ?.settings_data;
        } else {
            configData = this.props?.sp_config_data?.settings_data;
        }
        return configData?.enable_sp_rating;
    }

    isProfitLossConfigEnabled() {
        if (!ConfigHelpers.isServiceProvider()) {
            return false;
        }
        if (ConfigHelpers.isServiceProvider() && !this.isCustomerRequests()) {
            return false;
        }
        const settingsData =
            this.state.viewData?.sp_config_data?.[0]?.settings_data;
        return settingsData?.isCheckedProfitLoss || false;
    }

    areRolesAuthorisedForProfitLoss() {
        const profitLossAuthorisedRoles =
            this.state.viewData?.sp_config_data?.[0]?.settings_data
                ?.profitLossAuthorisedRoles;
        const areRolesAuthorised = ConfigHelpers.doesUserHaveOneOfTheRole(
            profitLossAuthorisedRoles
        );
        return areRolesAuthorised;
    }

    isProjectProfitLossConfigEnalbed() {
        if (!ConfigHelpers.isServiceProvider() || !this.isCustomerRequests()) {
            return false;
        }

        const settingsData =
            this.state.viewData?.sp_config_data?.[0]?.settings_data;
        return settingsData?.project_profit_loss_is_checked || false;
    }

    areRolesAuthorisedForProjectProfitLoss() {
        const profitLossAuthorisedRoles =
            this.state.viewData?.sp_config_data?.[0]?.settings_data
                ?.project_profit_loss_authorised_roles;
        const areRolesAuthorised = ConfigHelpers.doesUserHaveOneOfTheRole(
            profitLossAuthorisedRoles
        );
        return areRolesAuthorised;
    }

    getSrvcTypeConfigData() {
        let config_data = this.props.srvcConfigData;
        //Get brand price_config_data for line_item & manday from history table [service_type_history]
        if (config_data) {
            let pricingConfigHis =
                this.state.viewData?.form_data
                    ?.srvc_type_history_pricing_config;
            if (pricingConfigHis && pricingConfigHis?.length > 0) {
                let priceConfigFrLineItem =
                    pricingConfigHis?.[0]
                        ?.srvc_type_pricing_config_for_line_item;
                if (priceConfigFrLineItem && priceConfigFrLineItem != '') {
                    config_data['srvc_type_pricing_config_for_line_item'] =
                        priceConfigFrLineItem;
                } else {
                    config_data['srvc_type_pricing_config_for_line_item'] =
                        '{}';
                }

                let priceConfigFrManday =
                    pricingConfigHis?.[0]?.srvc_type_pricing_config_for_manday;
                if (priceConfigFrManday && priceConfigFrManday != '') {
                    config_data['srvc_type_pricing_config_for_manday'] =
                        priceConfigFrManday;
                } else {
                    config_data['srvc_type_pricing_config_for_manday'] = '{}';
                }
            }
        }
        return config_data; //this.props.srvcConfigData;
    }

    getIsShowLineItemsToSPEnabled = () => {
        return this.props.srvcConfigData?.show_line_items_to_sp;
    };
    spPayoutsContainer = () => {
        const prefillFormData = convertDateFieldsToMoments(
            this.state.viewData?.form_data?.form_data,
            this.getAllFieldsMeta()
        );
        return (
            <>
                {Object.keys(this.getSpPayoutsConfig()).length > 0 && (
                    <Col xs={24} className=" gx-px-0 gx-mb-3">
                        {this.state.spPayoutsData && (
                            <Alert
                                message="**Unsaved changes(Click save to apply)"
                                type="warning"
                            />
                        )}
                        <SpPayouts
                            config={this.getSpPayoutsConfig()}
                            srvcConfigData={this.getConfigDataFrSrvcPrvdr()}
                            spPayoutsData={
                                this.state.spPayoutsData ||
                                prefillFormData?.sp_payouts_data ||
                                {}
                            }
                            onChange={(newData) => {
                                this.setState({ spPayoutsData: newData });
                            }}
                            vendorUsers={
                                this.state.viewData?.form_data
                                    ?.sp_payout_users_fr_vendors
                            }
                            // readOnly={true}
                            readOnly={
                                !this.state.viewData?.form_data
                                    ?.allow_to_add_sp_payout_tab
                            }
                            srvc_type_id={this.props.srvcDetails?.srvc_id}
                            title={this.getTitle()}
                            lineItemQTY={
                                this.state.viewData?.form_data
                                    ?.download_sp_line_item_data?.total_qty
                            }
                            totalRevenue={
                                this.state.viewData?.form_data
                                    ?.billing_final_amount
                            }
                        />
                    </Col>
                )}
            </>
        );
    };

    //here
    LineItemsContainer = (props) => {
        const prefillFormData = convertDateFieldsToMoments(
            this.state.viewData?.form_data?.form_data,
            this.getAllFieldsMeta()
        );
        return (
            <>
                {Object.keys(this.getLineItemConfig()).length > 0 &&
                    this.isProjectNature() &&
                    (!ConfigHelpers.isServiceProvider() ||
                        !this.isCustomerRequests() ||
                        this.getIsShowLineItemsToSPEnabled()) && (
                        <Col
                            xs={24}
                            className="gx-border gx-border-orange gx-py-3 gx-mb-3"
                        >
                            {this.state.lineItemsData &&
                                !ConfigHelpers.isServiceProvider() &&
                                !this.isCustomerRequests() && (
                                    <Alert
                                        message="**Unsaved changes(Click save to apply)"
                                        type="warning"
                                    />
                                )}
                            <LineItems
                                config={this.getLineItemConfig()}
                                srvcConfigData={this.getSrvcTypeConfigData()}
                                lineItemsData={
                                    this.state.lineItemsData ||
                                    prefillFormData?.line_items ||
                                    {}
                                }
                                onChange={(newData) => {
                                    this.setState({ lineItemsData: newData });
                                }}
                                isBrand
                                readOnly={
                                    ConfigHelpers.isServiceProvider() &&
                                    this.isCustomerRequests()
                                        ? true
                                        : false
                                }
                                isSrvcReqLock={getIsSrvcReqLock(
                                    prefillFormData,
                                    this.isOrgSrvcPrvdr()
                                )}
                                revisionsData={
                                    this.state.viewData?.form_data
                                        ?.revisions_data
                                }
                                title={this.getTitle()}
                                heading="Brand line items"
                                downloadLineItemData={
                                    this.state.viewData?.form_data
                                        ?.download_line_item_data || {}
                                }
                                isCustAccess={this.props.isCustAcces}
                            />
                        </Col>
                    )}
                {Object.keys(this.spGetLineItemConfig()).length > 0 && (
                    <Col
                        xs={24}
                        className="gx-border gx-border-orange gx-py-3 gx-mb-3"
                    >
                        {this.state.splineItemsData &&
                            ConfigHelpers.isServiceProvider() && (
                                <Alert
                                    message="**Unsaved changes(Click save to apply)"
                                    type="warning"
                                />
                            )}
                        <LineItems
                            config={this.spGetLineItemConfig()}
                            srvcConfigData={this.getConfigDataFrSrvcPrvdr()}
                            lineItemsData={
                                this.state.splineItemsData ||
                                prefillFormData?.sp_line_items ||
                                {}
                            }
                            onChange={(newData) => {
                                this.setState({ splineItemsData: newData });
                            }}
                            readOnly={
                                !ConfigHelpers.isServiceProvider()
                                    ? true
                                    : false
                            }
                            isSrvcReqLock={getIsSrvcReqLock(
                                prefillFormData,
                                this.isOrgSrvcPrvdr()
                            )}
                            srvc_type_id={this.props.srvcDetails?.srvc_id}
                            revisionsData={
                                this.state.viewData?.form_data
                                    ?.sp_revisions_data
                            }
                            title={this.getTitle()}
                            heading="Service provider line items"
                            downloadLineItemData={
                                this.state.viewData?.form_data
                                    ?.download_sp_line_item_data || {}
                            }
                            allow_sp_line_items_download={
                                this.state.viewData?.form_data
                                    ?.allow_sp_line_items_download
                            }
                        />
                    </Col>
                )}
            </>
        );
    };

    getTitle() {
        const { editorItem } = this.props;
        var editorTitle =
            showExtOrderId(this.props.srvcConfigData) &&
            editorItem?.ext_order_id
                ? editorItem?.ext_order_id
                : editorItem?.title;
        return editorTitle;
    }

    isOrgSrvcPrvdr() {
        return ConfigHelpers.isSrvcPrvdrBySrvcReqId(
            this.state.viewData?.form_data
        );
    }

    setCurrentTab(currentTab) {
        this.setState({ selectedCurrentTab: currentTab });
    }
    setTransitionOverviewActiveTab(currentTab) {
        this.setState({ transitionOverviewActiveTab: currentTab });
    }

    getCollapsableSpecificFieldsMeta() {
        let returnData = [];
        let srvcConfigData = this.props.srvcConfigData;
        let currentSelectedStatus = this.props.editorItem?.status?.key;
        let statuses = this.state.viewData?.statuses;
        if (statuses && statuses.length > 0) {
            statuses.forEach((singleStatus) => {
                let collapsableSpecificLabelName = `collapsable_specific_fields_fr_status_${singleStatus.value}_label`;
                let collapsableSpecificFieldsKey = `collapsable_specific_fields_fr_status_${singleStatus.value}`;
                let selectedCollapsableSpecificFields =
                    srvcConfigData?.[collapsableSpecificFieldsKey];
                if (
                    srvcConfigData?.srvc_cust_fields_json &&
                    selectedCollapsableSpecificFields &&
                    selectedCollapsableSpecificFields.length > 0
                ) {
                    returnData.push(
                        <div className="gx-mt-2 mb-3">
                            <Collapse defaultActiveKey={currentSelectedStatus}>
                                <Collapse.Panel
                                    header={
                                        <h3 className="gx-d-inline-flex gx-m-0">
                                            {' '}
                                            {srvcConfigData?.[
                                                collapsableSpecificLabelName
                                            ] || singleStatus.title}{' '}
                                        </h3>
                                    }
                                    key={singleStatus.value}
                                >
                                    <FormBuilder
                                        key="collapsable_specific_fields"
                                        meta={this.getCollapsableFieldsMetaFrmConfig(
                                            srvcConfigData,
                                            selectedCollapsableSpecificFields
                                        )}
                                        form={this.formRef}
                                    />
                                </Collapse.Panel>
                            </Collapse>
                        </div>
                    );
                }
            });
        }
        return returnData;
    }

    getCollapsableFieldsMetaFrmConfig(
        srvcConfigData,
        selectedCollapsableSpecificFields
    ) {
        let columns = srvcConfigData?.srvc_cust_fields_json_colspan || 2;
        let customFields = this.getCustomFieldsFrmConfig()?.fields;

        let collapsableFieldsMetaFrStatus = [];
        if (customFields && customFields.length > 0) {
            let custFilteredFields = customFields?.filter((item) =>
                selectedCollapsableSpecificFields?.includes(item.key)
            );

            //For BarcodeScanner
            let barcode_scanner_key = custFilteredFields?.filter(
                (singleItem) => singleItem?.type == 'Barcode_scanner'
            )?.[0]?.key;
            let barcode_scanner_prefix_key = `${barcode_scanner_key}_scanner`;
            let barcodeScannerFields = customFields?.filter(
                (item) => item.key == barcode_scanner_prefix_key
            );

            collapsableFieldsMetaFrStatus = [
                ...custFilteredFields,
                ...barcodeScannerFields,
            ];
        }

        const meta = {
            columns: columns,
            formItemLayout: null,
            fields: collapsableFieldsMetaFrStatus,
        };
        return meta;
    }

    getSelectedCollapsableSpecificFields(excludeStatus = []) {
        let srvcConfigData = this.props.srvcConfigData;
        let statuses = this.state.viewData?.statuses;
        let SelectedCollapsableSpecificFields = [];
        if (statuses && statuses.length > 0) {
            statuses.forEach((singleStatus) => {
                if (!excludeStatus.includes(singleStatus.value)) {
                    let collapsableSpecificFieldsKey = `collapsable_specific_fields_fr_status_${singleStatus.value}`;
                    let collapsableSpecificFields =
                        srvcConfigData?.[collapsableSpecificFieldsKey];
                    if (
                        collapsableSpecificFields &&
                        collapsableSpecificFields.length > 0
                    ) {
                        SelectedCollapsableSpecificFields.push(
                            ...collapsableSpecificFields
                        );
                    }
                }
            });
        }
        return SelectedCollapsableSpecificFields;
    }

    getFieldVsAuthorityMapping() {
        let fieldVsAuthorityMapping = {};
        let isServiceProvider = ConfigHelpers.isServiceProvider();
        let authoritiesMeta = [];
        if (isServiceProvider) {
            authoritiesMeta = this.getSPAuthoritySelectorMeta();
        } else {
            authoritiesMeta = this.getAuthoritySelectorMeta();
        }
        const formData = this.state.viewData?.form_data?.form_data || {};
        if (authoritiesMeta?.length > 0) {
            authoritiesMeta.forEach((singleAuthorityMeta) => {
                const role_id = singleAuthorityMeta['role_id'];
                const keyInFormData = singleAuthorityMeta['key'];
                let configData = isServiceProvider
                    ? this.props?.spAuthoritiesConfigData ||
                      this.state.viewData?.sp_authorities_config_data
                    : this.props.srvcConfigData;

                const authoritySpecificFields =
                    configData?.[`srvc_authority_${role_id}_specific_fields`];
                if (authoritySpecificFields?.length > 0) {
                    authoritySpecificFields.forEach((singleFieldId) => {
                        if (
                            fieldVsAuthorityMapping[singleFieldId] == undefined
                        ) {
                            fieldVsAuthorityMapping[singleFieldId] = [];
                        }
                        let authorityFrTheRole = formData[keyInFormData];
                        if (authorityFrTheRole) {
                            fieldVsAuthorityMapping[singleFieldId] = [
                                ...fieldVsAuthorityMapping[singleFieldId],
                                authorityFrTheRole,
                            ];
                        }
                    });
                }
            });
        }
        return fieldVsAuthorityMapping;
    }

    hasAccessToEditDeployment() {
        const deploymentConfigData = this.getDeploymentConfigData();
        const possible_roles_to_edit = ConfigHelpers.isServiceProvider()
            ? deploymentConfigData?.sp_deployment_who_can_edit
            : deploymentConfigData?.deployment_who_can_edit;
        if (possible_roles_to_edit?.length > 0) {
            return ConfigHelpers.doesUserHaveOneOfTheRole(
                possible_roles_to_edit
            );
        }
        return true;
    }

    getRatingDetails = (ratingTab, configData, srvcOrVerticalNature) => {
        let loggedInUserRole = ConfigHelpers.getPrimaryUserRole()?.role_id;
        let authorityUsersList = [];
        let authorityRoleList = [];
        let assigneeUsersList = [];
        let assigneeRoleList = [];
        let ratingDetails = { authority: undefined };
        if (srvcOrVerticalNature == 'project_based') {
            ratingDetails['assignees'] = undefined;
        }
        const loggedInUserId = ConfigHelpers.getUserUUID();
        let role_wise_authorities_users_list =
            ratingTab == 'brand'
                ? this.state.viewData?.role_wise_authorities_users_list
                : this.state.viewData
                      ?.srvc_prvdr_role_wise_authorities_users_list;
        const srvcReqData = this.state.viewData?.form_data?.form_data;
        const role_list_vs_users = this.state.viewData?.role_list_vs_users;
        const role_list = this.state.viewData?.role_list;
        const assignees = this.state.viewData?.form_data?.assignees;
        role_wise_authorities_users_list.forEach(
            (singleRoleWiseAuthoritiesUsersList) => {
                const ratingTypeKeyFrAuthority =
                    ratingTab == 'srvcPrvdr'
                        ? `sp_rating_type_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                        : `rating_type_${singleRoleWiseAuthoritiesUsersList?.role_id}`;
                if (configData?.[ratingTypeKeyFrAuthority] != 'static_user') {
                    const prefixAuthorityRatingKey =
                        ratingTab == 'srvcPrvdr'
                            ? 'sp_authority_'
                            : 'authority_';
                    const authorityRatingKey = `${prefixAuthorityRatingKey}${singleRoleWiseAuthoritiesUsersList?.role_id}`;
                    if (
                        srvcReqData?.[`authority_${loggedInUserRole}`] ==
                        loggedInUserId
                    ) {
                        if (
                            configData?.[authorityRatingKey] == loggedInUserRole
                        ) {
                            const RatingTemplateKey =
                                ratingTab == 'srvcPrvdr'
                                    ? `sp_rating_template_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                                    : `rating_template_${singleRoleWiseAuthoritiesUsersList?.role_id}`;
                            const selectedRatingTemplate =
                                configData?.[RatingTemplateKey];
                            let filteredUserList = [];
                            if (
                                srvcReqData?.[
                                    `authority_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                                ] &&
                                selectedRatingTemplate
                            ) {
                                filteredUserList =
                                    singleRoleWiseAuthoritiesUsersList?.options?.filter(
                                        (singleUser) =>
                                            singleUser?.value ==
                                            srvcReqData?.[
                                                `authority_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                                            ]
                                    );
                                if (filteredUserList?.length > 0) {
                                    authorityUsersList.push(
                                        ...filteredUserList
                                    );
                                    authorityRoleList.push({
                                        label: singleRoleWiseAuthoritiesUsersList?.label,
                                        value: singleRoleWiseAuthoritiesUsersList?.role_id,
                                    });
                                }
                                // const filterUserFrRole = configData?.[authorityRatingKey];
                                if (!ratingDetails['authority']) {
                                    ratingDetails['authority'] = {
                                        [singleRoleWiseAuthoritiesUsersList?.role_id]:
                                            {
                                                users: filteredUserList,
                                                template:
                                                    selectedRatingTemplate,
                                            },
                                    };
                                } else {
                                    ratingDetails['authority'][
                                        singleRoleWiseAuthoritiesUsersList?.role_id
                                    ] = {
                                        users: [...filteredUserList],
                                        template: selectedRatingTemplate,
                                    };
                                }
                            }
                        }
                    }
                } else {
                    const prefixAuthorityStaticKey =
                        ratingTab == 'srvcPrvdr'
                            ? 'sp_static_user_'
                            : 'static_user';
                    const staticFrAuthorityKey = `${prefixAuthorityStaticKey}${singleRoleWiseAuthoritiesUsersList?.role_id}`;
                    if (
                        configData?.[staticFrAuthorityKey]?.value ==
                        loggedInUserId
                    ) {
                        const RatingTemplateKey =
                            ratingTab == 'srvcPrvdr'
                                ? `sp_rating_template_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                                : `rating_template_${singleRoleWiseAuthoritiesUsersList?.role_id}`;
                        const selectedRatingTemplate =
                            configData?.[RatingTemplateKey];
                        let filteredUserList = [];
                        if (
                            srvcReqData?.[
                                `authority_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                            ] &&
                            selectedRatingTemplate
                        ) {
                            filteredUserList =
                                singleRoleWiseAuthoritiesUsersList?.options?.filter(
                                    (singleUser) =>
                                        singleUser.value ==
                                        srvcReqData?.[
                                            `authority_${singleRoleWiseAuthoritiesUsersList?.role_id}`
                                        ]
                                );
                            if (filteredUserList?.length > 0) {
                                authorityUsersList.push(...filteredUserList);
                                authorityRoleList.push({
                                    label: singleRoleWiseAuthoritiesUsersList?.label,
                                    value: singleRoleWiseAuthoritiesUsersList?.role_id,
                                });
                            }
                            if (!ratingDetails['authority']) {
                                ratingDetails['authority'] = {
                                    [singleRoleWiseAuthoritiesUsersList?.role_id]:
                                        {
                                            users: filteredUserList,
                                            template: selectedRatingTemplate,
                                        },
                                };
                            } else {
                                ratingDetails['authority'][
                                    singleRoleWiseAuthoritiesUsersList?.role_id
                                ] = {
                                    users: [...filteredUserList],
                                    template: selectedRatingTemplate,
                                };
                            }
                        }
                    }
                }
            }
        );
        console.log('singleDeployPossibleRole configData', configData);
        if (
            configData.deployment_possible_roles &&
            srvcOrVerticalNature == 'project_based'
        ) {
            configData.deployment_possible_roles.forEach(
                (singleDeployPossibleRole) => {
                    const ratingTypeKeyFrAssignee =
                        ratingTab == 'srvcPrvdr'
                            ? `sp_rating_type_fr_deployment_${singleDeployPossibleRole}`
                            : `rating_type_fr_deployment_${singleDeployPossibleRole}`;
                    if (
                        configData?.[ratingTypeKeyFrAssignee] != 'static_user'
                    ) {
                        const prefixAssigneeRatingKey =
                            ratingTab == 'srvcPrvdr'
                                ? 'sp_authority_fr_deployment_'
                                : 'authority_fr_deployment_';
                        const assigneeRatingKey = `${prefixAssigneeRatingKey}${singleDeployPossibleRole}`;
                        if (
                            srvcReqData?.[`authority_${loggedInUserRole}`] ==
                            loggedInUserId
                        ) {
                            if (
                                configData?.[assigneeRatingKey] ==
                                loggedInUserRole
                            ) {
                                const RatingTemplateKey =
                                    ratingTab == 'srvcPrvdr'
                                        ? `sp_rating_template_fr_deployment${singleDeployPossibleRole}`
                                        : `rating_template_fr_deployment_${singleDeployPossibleRole}`;

                                const selectedRatingTemplate =
                                    configData?.[RatingTemplateKey];
                                // const filterUserFrRole = configData?.[assigneeRatingKey];
                                let filteredUserList = [];
                                if (assignees?.[0] && selectedRatingTemplate) {
                                    filteredUserList = role_list_vs_users?.[
                                        singleDeployPossibleRole
                                    ]?.filter((singleUser) =>
                                        assignees[0]?.includes(singleUser.value)
                                    );
                                    if (filteredUserList?.length > 0) {
                                        let roleData = role_list?.filter(
                                            (singleRole) =>
                                                singleRole.value ==
                                                singleDeployPossibleRole
                                        );
                                        assigneeRoleList.push(...roleData);
                                        assigneeUsersList.push(
                                            ...filteredUserList
                                        );

                                        if (!ratingDetails['assignees']) {
                                            ratingDetails['assignees'] = {
                                                [singleDeployPossibleRole]: {
                                                    users: filteredUserList,
                                                    template:
                                                        selectedRatingTemplate,
                                                },
                                            };
                                        } else {
                                            ratingDetails['assignees'][
                                                singleDeployPossibleRole
                                            ] = {
                                                users: filteredUserList,
                                                template:
                                                    selectedRatingTemplate,
                                            };
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        const loggedInUserId = ConfigHelpers.getUserUUID();
                        const prefixAssigneeStaticKey =
                            ratingTab == 'srvcPrvdr'
                                ? 'sp_static_user_fr_deployment_'
                                : 'static_user_fr_deployment_';
                        const staticFrAssigneeKey = `${prefixAssigneeStaticKey}${singleDeployPossibleRole}`;
                        if (
                            configData?.[staticFrAssigneeKey]?.value ==
                            loggedInUserId
                        ) {
                            const RatingTemplateKey =
                                ratingTab == 'srvcPrvdr'
                                    ? `sp_rating_template_fr_deployment${singleDeployPossibleRole}`
                                    : `rating_template_fr_deployment_${singleDeployPossibleRole}`;
                            const selectedRatingTemplate =
                                configData?.[RatingTemplateKey];
                            let filteredUserList = [];
                            if (assignees?.[0] && selectedRatingTemplate) {
                                filteredUserList = role_list_vs_users?.[
                                    singleDeployPossibleRole
                                ]?.filter((singleUser) =>
                                    assignees[0]?.includes(singleUser.value)
                                );
                                if (filteredUserList?.length > 0) {
                                    let roleData = role_list?.filter(
                                        (singleRole) =>
                                            singleRole.value ==
                                            singleDeployPossibleRole
                                    );
                                    assigneeRoleList.push(...roleData);
                                    assigneeUsersList.push(...filteredUserList);

                                    if (!ratingDetails['assignees']) {
                                        ratingDetails['assignees'] = {
                                            [singleDeployPossibleRole]: {
                                                users: filteredUserList,
                                                template:
                                                    selectedRatingTemplate,
                                            },
                                        };
                                    } else {
                                        ratingDetails['assignees'][
                                            singleDeployPossibleRole
                                        ] = {
                                            users: filteredUserList,
                                            template: selectedRatingTemplate,
                                        };
                                    }
                                }
                            }
                        }
                    }
                }
            );
        }

        if (ratingDetails['authority']) {
            ratingDetails['authority']['finalUserList'] = authorityUsersList;
            ratingDetails['authority']['possible_roles'] = authorityRoleList;
        }
        if (ratingDetails['assignees']) {
            ratingDetails['assignees']['finalUserList'] = assigneeUsersList;
            ratingDetails['assignees']['possible_roles'] = assigneeRoleList;
        }
        console.log('rating in itemEditor', ratingDetails);
        return ratingDetails;
    };

    isSpPayoutConfigEnabled() {
        return this.state.viewData?.form_data?.sp_config_data?.[0]
            ?.settings_data?.enable_sp_payouts;
    }

    getRatingDetailsFrSrvcPrvdr(ratingTab, configData, srvcOrVerticalNature) {
        let ratingDetails;
        if (ConfigHelpers.isServiceProvider()) {
            if (ratingTab == 'srvcPrvdr' && this.isCustomerRequests()) {
                return this.getRatingDetails(
                    ratingTab,
                    configData,
                    srvcOrVerticalNature
                );
            }
        }
        return ratingDetails;
    }

    displayAvgGaiRatingOfSubTasks(data) {
        const avgGaiRating = getAvgGaiRatingOfSubTasks(data);
        if (!avgGaiRating) return;

        return (
            <GaiWrapper>
                <Tooltip title="This is Ai Generated">
                    <img
                        src="https://static.wify.co.in/images/website/tms/star.png"
                        className="wy-rating-img-small"
                    />{' '}
                    {avgGaiRating} Avg.
                </Tooltip>
            </GaiWrapper>
        );
    }

    isUserRestrictedToUpdateRequests() {
        if (ConfigHelpers.isServiceProvider()) return false;

        const userServiceAccess =
            this.state.viewData?.form_data?.access_service_routes || [];
        const userRights = getUserRightsFrService(
            this.props.srvcDetails?.srvc_id,
            userServiceAccess
        );
        // restricted, if user not having CREATE OR UPDATE rights
        const userRestricted = !(
            userRights?.includes('CREATE') || userRights?.includes('UPDATE')
        );
        return userRestricted;
    }

    render() {
        const { editorItem } = this.props;
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
            currentStep,
            srvcDetails,
            fileSections,
            micSections,
            cameraSections,
            isExecutingDynamicFormLogic,
            selectedCurrentTab,
            transitionOverviewActiveTab,
            showOrgDetails,
        } = this.state;
        const isSpPayoutConfigEnabled =
            this.state.viewData?.form_data?.sp_config_data?.[0]?.settings_data
                ?.enable_sp_payouts;
        var editorTitle = this.getTitle();
        var editMode = true;
        if (editorTitle == undefined) {
            editorTitle = 'Add new ' + srvcDetails?.srvc_title;
            editMode = false;
        } else {
            editorTitle = 'Edit - ' + editorTitle;
        }
        let userRights = [];
        userRights = ConfigHelpers?.getUserRights(srvcDetails?.srvc_id);
        const prvdr_assg_timestamp =
            viewData?.form_data?.form_data?.prvdr_assg_timestamp?.toString();
        let prefillFormData = convertDateFieldsToMoments(
            this.state.viewData?.form_data?.form_data,
            this.getAllFieldsMeta()
        );
        const feedbackData = prefillFormData?.feedback_data;
        const allFileUploadersReady = this.getAllFileUploadersReady();
        const reqSrvcDate = prefillFormData?.request_req_date;
        const reqStartTime = prefillFormData?.start_time;
        const reqEndTime = prefillFormData?.end_time;
        const isActiveStatus =
            viewData?.form_data?.status.status_type == 'ACTIVE' ? true : false;
        const openCollapseByDefault =
            !editMode || (!this.isProjectNature() && isActiveStatus);
        const custom_fields = decodeFieldsMetaFrmJson(
            this.getCustomFieldsJson()
        );
        const is_enable_auto_assign_authorities_refresh_btn =
            this.state.viewData?.form_data
                ?.is_enable_auto_assign_authorities_refresh_btn;

        const feedbackRatingFieldKey = feedbackData?.rating_field_key;
        const ratingMeta = feedbackData?.form_meta?.find(
            (item) => item.key === feedbackRatingFieldKey
        );
        const userFeedbackData = {
            feedbackRatingFieldKey,
            rated: feedbackData?.form_data[feedbackRatingFieldKey] || 0,
            maxRating: ratingMeta?.widgetProps?.count,
        };

        const showProfitLossTab =
            this.isProfitLossConfigEnabled() &&
            this.areRolesAuthorisedForProfitLoss() &&
            editMode;

        const showProjectProfitLossTab =
            this.isProjectProfitLossConfigEnalbed() &&
            this.areRolesAuthorisedForProjectProfitLoss() &&
            editMode;
        const authoritySelectorPrefillFormData = prefillFormData;

        //remove inactive brand authorities from prefill form data
        prefillFormData = this.sanitizeAuthorityFields(
            prefillFormData,
            this.getAuthoritySelectorMeta()
        );

        //remove inactive SP authorities from prefill form data
        prefillFormData = this.sanitizeAuthorityFields(
            prefillFormData,
            this.getSPAuthoritySelectorMeta()
        );

        /**
         * Retrieves the first appearing "open" status transition from the service request form data.
         *
         * @returns {Object|undefined} The status transition object with key 'open', or undefined if not found.
         */
        const latestOpenStatus = viewData?.form_data?.status_transitions?.find(
            ({ key }) => key === 'open'
        );

        /**
         * Retrieves the first appearing "closed" status transition from the service request form data.
         *
         * @returns {Object|undefined} The status transition object with key 'closed', or undefined if not found.
         */
        const latestClosedStatus =
            viewData?.form_data?.status_transitions?.find(
                ({ key }) => key === 'closed'
            );

        const isSpReassignmentAllowed =
            viewData?.form_data?.is_sp_reassignment_allowed;
        let userRestricted = this.isUserRestrictedToUpdateRequests() || false;

        return visible ? (
            <Modal
                maskClosable={false}
                className={`${this.props?.editorItem?.is_deleted == true ? 'wy-deleted-ticket' : ''}`}
                title={
                    <div className="gx-d-flex gx-justify-content-start gx-align-items-center gx-pr-5">
                        <div>
                            <i
                                className={`icon ${srvcDetails?.srvc_icon} gx-mr-2 h1`}
                            ></i>{' '}
                            {editorTitle}
                        </div>
                        {editorItem?.title && (
                            <div className="gx-align-items-center gx-text-blue">
                                <Tooltip title={`Copy ${editorItem?.title}`}>
                                    <CopyToClipboard
                                        onCopy={() =>
                                            message.success(
                                                `Copied! ${editorItem?.title}`
                                            )
                                        }
                                        text={editorItem?.title}
                                    >
                                        <CopyOutlined />
                                    </CopyToClipboard>
                                </Tooltip>
                            </div>
                        )}
                        {viewData == undefined ? (
                            <p className="gx-text-red">{error}</p>
                        ) : (
                            editMode && (
                                <div className="gx-d-flex gx-align-items-center gx-p-2 wy-bg-v-light-grey wy-br-5 gx-ml-3 wy-hover-toggle ">
                                    <DeleteSrvcReqButton
                                        className="gx-ml-2 gx-mb-0"
                                        editMode={editMode}
                                        handleOk={this.handleOk}
                                        serviceReqDetails={viewData?.form_data}
                                        srvcId={srvcDetails?.srvc_id}
                                        urlToSubmit={
                                            '/services/modify/' +
                                            viewData?.form_data?.srvc_type_id +
                                            '/' +
                                            viewData?.form_data?.id
                                        }
                                        onDataModified={(entry_id) => {
                                            this.props.onDataModified(entry_id);
                                            this.updateClosureToParent();
                                        }}
                                    />
                                </div>
                            )
                        )}
                    </div>
                }
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={1500}
                style={{ marginTop: '-70px' }}
                bodyStyle={{
                    minHeight: '85vh',
                    padding: '18px',
                    paddingTop: '0px',
                }}
                footer={null}
                onCancel={this.handleCancel}
            >
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <>
                        {getIsSrvcReqLock(
                            prefillFormData,
                            this.isOrgSrvcPrvdr()
                        ) && (
                            <div className="gx-mt-2">
                                <Alert
                                    message="This request is locked for billing, you will not be able to change Line items, Billing, P&L and Deployment charge basis"
                                    type="info"
                                    closable
                                />
                            </div>
                        )}
                        <Row>
                            {!editMode && (
                                <Col xs={24} className="gx-my-1">
                                    <Collapse>
                                        <Collapse.Panel
                                            header={
                                                <span className="gx-text-primary">
                                                    <UploadOutlined className="gx-mr-2" />
                                                    Click here for Bulk creation
                                                </span>
                                            }
                                        >
                                            <div>
                                                <BulkUploader
                                                    // demoMode
                                                    // renderFormsForRows
                                                    // debugMode
                                                    onDataModified={(
                                                        entry_ids
                                                    ) =>
                                                        this.tellParentToRefreshList(
                                                            0
                                                        )
                                                    }
                                                    submitUrl={this.submitUrl}
                                                    dataProto={this.getFieldsMetaFrBulkUpload()}
                                                    orgSettingsData={
                                                        this.props
                                                            .orgSettingsData
                                                    }
                                                    timeFormatMsg
                                                />
                                            </div>
                                        </Collapse.Panel>
                                    </Collapse>
                                </Col>
                            )}
                            <Col xs={24}>
                                <Form
                                    className="gx-w-100"
                                    layout="vertical"
                                    initialValues={
                                        this.state.editMode
                                            ? prefillFormData
                                            : {}
                                    }
                                    ref={this.formRef}
                                    onFinish={(data) => {
                                        this.submitForm(data);
                                    }}
                                    onFinishFailed={(errorInfo) =>
                                        this.onFinishFailed(errorInfo)
                                    }
                                    disabled={isExecutingDynamicFormLogic}
                                    onValuesChange={(
                                        changedValues,
                                        allValues
                                    ) => {
                                        if (this.isDynamicForm()) {
                                            this.onFormValueChanged(
                                                changedValues,
                                                allValues
                                            );
                                        } else {
                                            console.log('Not a dynamic form');
                                        }
                                        if (
                                            this.isOrgSrvcPrvdr() &&
                                            this.isSPFieldsDynamicForm() &&
                                            ConfigHelpers.isServiceProvider()
                                        ) {
                                            this.onFormValueChanged(
                                                changedValues,
                                                allValues,
                                                true //sp fields are dynamic trigger for lambda
                                            );
                                        }
                                    }}
                                >
                                    <Tabs
                                        onChange={(currentTab) =>
                                            this.setCurrentTab(currentTab)
                                        }
                                        defaultActiveKey={
                                            this.state.selectedCurrentTab
                                        }
                                    >
                                        <Tabs.TabPane
                                            tab="Request Details"
                                            key="request_details"
                                            forceRender
                                            className="gx-pl-1"
                                        >
                                            <Row>
                                                <Col
                                                    xs={24}
                                                    md={12}
                                                    className="gx-border-right gx-mt-3 gx-p-0 gx-mb-md-3 gx-mb-1"
                                                >
                                                    <Row>
                                                        <Col
                                                            xs={24}
                                                            className="gx-p-0"
                                                        >
                                                            {editMode && (
                                                                <div className="gx-d-flex">
                                                                    <StatusChanger
                                                                        possibleStatus={
                                                                            this
                                                                                .props
                                                                                .possibleStatus
                                                                        }
                                                                        currentStatus={
                                                                            viewData
                                                                                ?.form_data
                                                                                ?.status
                                                                                .key
                                                                        }
                                                                        urlToSubmit={
                                                                            this
                                                                                .submitUrl +
                                                                            '/' +
                                                                            this
                                                                                .props
                                                                                .editorItem
                                                                                .id
                                                                        }
                                                                        srvc_id={
                                                                            this
                                                                                .props
                                                                                .srvcDetails
                                                                                ?.srvc_id
                                                                        }
                                                                        onDataModified={(
                                                                            entry_id
                                                                        ) => {
                                                                            // refresh subtasks list
                                                                            this.props.onDataModified(
                                                                                entry_id
                                                                            );
                                                                        }}
                                                                        tagClassName={
                                                                            'h3'
                                                                        }
                                                                    />
                                                                    {feedbackData && (
                                                                        <StarRatingCompact
                                                                            rated={
                                                                                userFeedbackData?.rated
                                                                            }
                                                                            maxRating={
                                                                                userFeedbackData?.maxRating
                                                                            }
                                                                            timestamp={
                                                                                feedbackData?.timestamp
                                                                            }
                                                                        />
                                                                    )}
                                                                </div>
                                                            )}
                                                        </Col>
                                                        {!editMode && (
                                                            <Col
                                                                xs={24}
                                                                lg={12}
                                                                className="gx-pl-lg-0 gx-pt-sm-2 gx-pt-lg-0 gx-pl-0 gx-mt-0"
                                                            >
                                                                {!editMode &&
                                                                    (this.props
                                                                        .srvcConfigData
                                                                        ?.srvc_enable_srvc_prvdr ||
                                                                        viewData
                                                                            ?.form_data
                                                                            ?.srvc_prvdr) && (
                                                                        <PreSrvcPrvdrSelector
                                                                            allSrvcPrvdrs={
                                                                                this
                                                                                    .props
                                                                                    .all_srvc_prvdrs
                                                                            }
                                                                            allowedSrvcPrvdrs={
                                                                                this
                                                                                    .props
                                                                                    .srvcConfigData
                                                                                    ?.srvc_possible_prvdrs
                                                                            }
                                                                            currentSrvcPrvdr={
                                                                                this
                                                                                    .props
                                                                                    .srvcConfigData
                                                                                    ?.srvc_default_provider
                                                                            }
                                                                            currentPrvdrAssgTimestamp={
                                                                                ''
                                                                            }
                                                                            urlToSubmit={
                                                                               this.capacityApiUrl
                                                                            }
                                                                            onDataModified={(
                                                                                entry_id
                                                                            ) => {
                                                                                // refresh subtasks list
                                                                                this.props.onDataModified(
                                                                                    entry_id
                                                                                );
                                                                            }}
                                                                            isSpReassignmentAllowed={
                                                                                isSpReassignmentAllowed
                                                                            }
                                                                        />
                                                                    )}
                                                            </Col>
                                                        )}

                                                        <Col
                                                            xs={24}
                                                            lg={12}
                                                            className="gx-pl-lg-0 gx-pt-sm-2 gx-pt-lg-0 gx-pl-0 gx-mt-0"
                                                        >
                                                            {editMode &&
                                                                (this.props
                                                                    .srvcConfigData
                                                                    ?.srvc_enable_srvc_prvdr ||
                                                                    viewData
                                                                        ?.form_data
                                                                        ?.srvc_prvdr) && (
                                                                    <SrvcPrvdrSelector
                                                                        allSrvcPrvdrs={
                                                                            this
                                                                                .props
                                                                                .all_srvc_prvdrs
                                                                        }
                                                                        allowedSrvcPrvdrs={
                                                                            this
                                                                                .props
                                                                                .srvcConfigData
                                                                                ?.srvc_possible_prvdrs
                                                                        }
                                                                        currentSrvcPrvdr={
                                                                            prefillFormData.new_prvdr
                                                                        }
                                                                        currentPrvdrAssgTimestamp={
                                                                            prvdr_assg_timestamp
                                                                        }
                                                                        urlToSubmit={
                                                                            this
                                                                                .submitUrl +
                                                                            '/' +
                                                                            this
                                                                                .props
                                                                                .editorItem
                                                                                .id
                                                                        }
                                                                        onDataModified={(
                                                                            entry_id
                                                                        ) => {
                                                                            // refresh subtasks list
                                                                            this.props.onDataModified(
                                                                                entry_id
                                                                            );
                                                                        }}
                                                                        isSpReassignmentAllowed={
                                                                            isSpReassignmentAllowed
                                                                        }
                                                                    />
                                                                )}
                                                        </Col>
                                                        {editMode &&
                                                            ConfigHelpers.isServiceProvider() &&
                                                            !this.props
                                                                .isCustAcces &&
                                                            this.state
                                                                .showOrgDetails &&
                                                            this.props
                                                                .srvcConfigData
                                                                ?.srvc_type_name && (
                                                                <Col
                                                                    xs={24}
                                                                    lg={12}
                                                                    className="gx-pl-lg-0 gx-pt-sm-2 gx-pt-lg-0 gx-pl-0 gx-mt-0"
                                                                >
                                                                    <BrandCard
                                                                        brandLogo={
                                                                            editorItem
                                                                                ?.org
                                                                                ?.icon_path
                                                                        }
                                                                        brandName={
                                                                            editorItem
                                                                                ?.org
                                                                                ?.label
                                                                        }
                                                                        serviceType={
                                                                            this
                                                                                .props
                                                                                ?.srvcConfigData
                                                                                ?.srvc_type_name
                                                                        }
                                                                    />
                                                                </Col>
                                                            )}
                                                    </Row>

                                                    {this.getAuthoritySelectorMeta() &&
                                                        this.getAuthoritySelectorMeta()
                                                            .length > 0 &&
                                                        this.shouldShowAuthoritySelectorMeta() && (
                                                            <AuthoritySelector
                                                                meta={this.getAuthoritySelectorMeta()}
                                                                initialValue={
                                                                    authoritySelectorPrefillFormData
                                                                }
                                                                onChange={(
                                                                    newRowData
                                                                ) => {
                                                                    // console.log('newRowData',newRowData)
                                                                    this.setAuthoritiesFields(
                                                                        newRowData
                                                                    );
                                                                }}
                                                                onDataModified={(
                                                                    entry_id
                                                                ) => {
                                                                    this.tellParentToRefreshList(
                                                                        editorItem?.id
                                                                    );
                                                                    this.refreshSelf(
                                                                        entry_id
                                                                    );
                                                                }}
                                                                // debugMode
                                                                allowEdit={
                                                                    !ConfigHelpers.isServiceProvider() ||
                                                                    !this.isCustomerRequests()
                                                                }
                                                                isCustomerRequests={this.isCustomerRequests()}
                                                                is_enable_auto_assign_authorities_refresh_btn={
                                                                    is_enable_auto_assign_authorities_refresh_btn
                                                                }
                                                            />
                                                        )}
                                                    {this.getSPAuthoritySelectorMeta() &&
                                                        this.getSPAuthoritySelectorMeta()
                                                            .length > 0 &&
                                                        this.shouldShowAuthoritySelectorMeta() && (
                                                            <AuthoritySelector
                                                                meta={this.getSPAuthoritySelectorMeta(
                                                                    true
                                                                )}
                                                                initialValue={
                                                                    authoritySelectorPrefillFormData
                                                                }
                                                                onChange={(
                                                                    newRowData
                                                                ) => {
                                                                    this.setAuthoritiesFields(
                                                                        newRowData
                                                                    );
                                                                }}
                                                                onDataModified={(
                                                                    entry_id
                                                                ) => {
                                                                    this.tellParentToRefreshList(
                                                                        editorItem?.id
                                                                    );
                                                                    this.refreshSelf(
                                                                        entry_id
                                                                    );
                                                                }}
                                                                spAuthority={
                                                                    true
                                                                }
                                                                allowEdit={ConfigHelpers.isServiceProvider()}
                                                                isCustomerRequests={this.isCustomerRequests()}
                                                                is_enable_auto_assign_authorities_refresh_btn={
                                                                    is_enable_auto_assign_authorities_refresh_btn
                                                                }
                                                            />
                                                        )}

                                                    {/* <Form
                                                        // {...formItemLayout}
                                                        
                                                        > */}
                                                    <Row
                                                        style={{
                                                            flexDirection:
                                                                'inherit',
                                                        }}
                                                    >
                                                        <Col
                                                            xs={24}
                                                            md={24}
                                                            className="gx-pl-0 gx-mb-3"
                                                        >
                                                            <Collapse
                                                                defaultActiveKey={
                                                                    openCollapseByDefault
                                                                        ? 'cust_details'
                                                                        : ''
                                                                }
                                                            >
                                                                <Collapse.Panel
                                                                    header={
                                                                        <h3 className="gx-d-inline-flex gx-m-0">
                                                                            {' '}
                                                                            Customer
                                                                            Details{' '}
                                                                        </h3>
                                                                    }
                                                                    key="cust_details"
                                                                    forceRender
                                                                >
                                                                    <Row>
                                                                        <Col
                                                                            xs={
                                                                                24
                                                                            }
                                                                            md={
                                                                                12
                                                                            }
                                                                            className="gx-pl-0"
                                                                        >
                                                                            <FormBuilder
                                                                                key="customer_info"
                                                                                //change here
                                                                                meta={this.getCustomerInfoMeta(
                                                                                    editMode
                                                                                )}
                                                                                form={
                                                                                    this
                                                                                        .formRef
                                                                                }
                                                                            />
                                                                        </Col>

                                                                        <Col
                                                                            xs={
                                                                                24
                                                                            }
                                                                            md={
                                                                                12
                                                                            }
                                                                            className="gx-pl-0"
                                                                        >
                                                                            <FormBuilder
                                                                                key="address_info"
                                                                                meta={this.getAddressInfoMeta()}
                                                                                form={
                                                                                    this
                                                                                        .formRef
                                                                                }
                                                                            />
                                                                        </Col>
                                                                    </Row>
                                                                </Collapse.Panel>
                                                            </Collapse>
                                                        </Col>

                                                        {/* {
                                                                Object.keys(this.getLineItemConfig()).length > 0 && 
                                                                this.isProjectNature() &&
                                                                (!ConfigHelpers.isServiceProvider() || !this.isCustomerRequests()) &&
                                                                <Col xs={24}  className="gx-pl-0 gx-mb-3">
                                                                    <Collapse
                                                                        defaultActiveKey = {
                                                                            openCollapseByDefault ? 'line_item_details' : ''
                                                                        }
                                                                        >
                                                                        <Collapse.Panel
                                                                            header={
                                                                                <h3 className="gx-d-inline-flex gx-m-0"> Brand line items </h3>
                                                                            }
                                                                            key="line_item_details" 
                                                                            >
                                                                            {
                                                                                this.state.lineItemsData &&
                                                                                <Alert 
                                                                                    message="**Unsaved changes(Click save to apply)"
                                                                                    type="warning" />
                                                                            }
                                                                            <LineItems
                                                                                config={this.getLineItemConfig()}
                                                                                srvcConfigData={this.getSrvcTypeConfigData()}
                                                                                lineItemsData={
                                                                                    this.state.lineItemsData || prefillFormData?.line_items || {}
                                                                                }
                                                                                onChange = {(newData)=>{
                                                                                    
                                                                                    this.setState({
                                                                                        lineItemsData : newData
                                                                                    })
                                                                                }}
                                                                                isBrand
                                                                                readOnly={(ConfigHelpers.isServiceProvider() && this.isCustomerRequests()) ? true : false}
                                                                                />
                                                                            
                                                                        </Collapse.Panel>
                                                                    </Collapse>    
                                                                </Col>
                                                            }
                                                            {
                                                                Object.keys(this.spGetLineItemConfig()).length > 0 && 
                                                                <Col xs={24}  className="gx-pl-0 gx-mb-3">
                                                                    <Collapse
                                                                        defaultActiveKey = {
                                                                            openCollapseByDefault ? 'sp_line_item_details' : ''
                                                                        }
                                                                        >
                                                                        <Collapse.Panel
                                                                            header={
                                                                                <h3 className="gx-d-inline-flex gx-m-0"> Service provider line items </h3>
                                                                            }
                                                                            key="sp_line_item_details" 
                                                                            >
                                                                            {
                                                                                this.state.splineItemsData &&
                                                                                <Alert 
                                                                                    message="**Unsaved changes(Click save to apply)"
                                                                                    type="warning" />
                                                                            }
                                                                            <LineItems
                                                                                config={this.spGetLineItemConfig()}
                                                                                srvcConfigData={this.getConfigDataFrSPLineItem()}
                                                                                lineItemsData={
                                                                                    this.state.splineItemsData || prefillFormData?.sp_line_items || {}
                                                                                }
                                                                                onChange = {(newData)=>{
                                                                                    this.setState({
                                                                                        splineItemsData : newData
                                                                                    })
                                                                                }}
                                                                                readOnly={!ConfigHelpers.isServiceProvider() ? true : false}
                                                                                />
                                                                        </Collapse.Panel>
                                                                    </Collapse>    
                                                                </Col>
                                                            } */}

                                                        {(custom_fields.length >
                                                            0 ||
                                                            this.getAuthoritySelectorMeta()
                                                                .length > 0 ||
                                                            this.getSpCustomFieldsMeta()
                                                                .length >
                                                                0) && (
                                                            <Col
                                                                xs={24}
                                                                className="gx-pl-0 gx-mb-3"
                                                            >
                                                                <Collapse
                                                                    defaultActiveKey={
                                                                        openCollapseByDefault
                                                                            ? 'cust_specific_details'
                                                                            : ''
                                                                    }
                                                                >
                                                                    <Collapse.Panel
                                                                        header={
                                                                            <h3 className="gx-d-inline-flex gx-m-0">
                                                                                {this
                                                                                    .props
                                                                                    .srvcConfigData
                                                                                    ?.rename_specific_details_label ||
                                                                                    'Specific Details'}{' '}
                                                                            </h3>
                                                                        }
                                                                        key="cust_specific_details"
                                                                        forceRender
                                                                    >
                                                                        {isExecutingDynamicFormLogic && (
                                                                            <>
                                                                                <Spin />
                                                                                {/* <div className="gx-loader-view gx-loader-position">
                                                                                        <CircularProgress/>
                                                                                    </div> */}
                                                                            </>
                                                                        )}

                                                                        <FormBuilder
                                                                            key="custom_req_fields"
                                                                            meta={this.getSpecificDetailsFieldsFrmConfig()}
                                                                            form={
                                                                                this
                                                                                    .formRef
                                                                            }
                                                                        />

                                                                        <div className="gx-mb-4">
                                                                            {(this
                                                                                .props
                                                                                .isCustAcces ||
                                                                                !this.isOrgSrvcPrvdr()) &&
                                                                                this.getCollapsableSpecificFieldsMeta()}
                                                                        </div>
                                                                        <FormBuilder
                                                                            key="authorities_custom_fields"
                                                                            meta={this.getAuthoritiesCustomFieldsFrmConfig()}
                                                                            form={
                                                                                this
                                                                                    .formRef
                                                                            }
                                                                        />
                                                                    </Collapse.Panel>
                                                                </Collapse>
                                                            </Col>
                                                        )}

                                                        <Col
                                                            xs={24}
                                                            className="gx-pl-0"
                                                        >
                                                            <Collapse
                                                                defaultActiveKey={
                                                                    openCollapseByDefault
                                                                        ? 'cust_req_details'
                                                                        : ''
                                                                }
                                                            >
                                                                <Collapse.Panel
                                                                    header={
                                                                        <h3 className="gx-d-inline-flex gx-m-0">
                                                                            Request
                                                                            details
                                                                        </h3>
                                                                    }
                                                                    key="cust_req_details"
                                                                    forceRender
                                                                >
                                                                    <FormBuilder
                                                                        key="request_info"
                                                                        meta={this.getRequestInfoMeta()}
                                                                        form={
                                                                            this
                                                                                .formRef
                                                                        }
                                                                    />
                                                                </Collapse.Panel>
                                                            </Collapse>
                                                        </Col>
                                                        {/* add booking compoent not in collapase direct */}
                                                        {this.state
                                                            .TMS250620631024 &&
                                                            this.state
                                                                .capacityData
                                                                ?.enable_capacity_module && (
                                                                <Col
                                                                    xs={24}
                                                                    className="gx-pl-0"
                                                                >
                                                                    <BookingSlotUp 
                                                                    org_id_ = {this.props.srvcConfigData?.srvc_default_provider}/>
                                                                </Col>
                                                            )}

                                                        {micSections.map(
                                                            (
                                                                singleMicSection,
                                                                index
                                                            ) => (
                                                                <Col
                                                                    xs={24}
                                                                    md={24}
                                                                    className="gx-pl-0"
                                                                    key={
                                                                        singleMicSection.key
                                                                    }
                                                                >
                                                                    {singleMicSection.title !=
                                                                        '' && (
                                                                        <h3 className="gx-mt-3">
                                                                            {
                                                                                singleMicSection.title
                                                                            }
                                                                            <hr className="gx-bg-dark"></hr>
                                                                        </h3>
                                                                    )}
                                                                    {/* For debugging */}
                                                                    {/* {
                                                                                this.state.micRecordingsBySection && 
                                                                                <Alert 
                                                                                    message={JSON.stringify(prefillFormData.mic_files?.[singleMicSection.key])}
                                                                                    type="info" />
                                                                            } */}
                                                                    <MicInputV2
                                                                        authToken={http_utils.getAuthToken()}
                                                                        prefixDomain={http_utils.getCDNDomain()}
                                                                        initialFiles={
                                                                            this
                                                                                .state
                                                                                .editMode
                                                                                ? prefillFormData
                                                                                      .mic_files?.[
                                                                                      singleMicSection
                                                                                          .key
                                                                                  ]
                                                                                : []
                                                                        }
                                                                        onFilesChanged={(
                                                                            files
                                                                        ) => {
                                                                            this.onMicFilesChanged(
                                                                                singleMicSection.key,
                                                                                files
                                                                            );
                                                                        }}
                                                                        onReadyStatusChange={(
                                                                            isReady
                                                                        ) => {
                                                                            this.onMicFileUploaderReadyChange(
                                                                                singleMicSection.key,
                                                                                isReady
                                                                            );
                                                                        }}
                                                                        readOnly={
                                                                            singleMicSection.disabled
                                                                        }
                                                                    />
                                                                </Col>
                                                            )
                                                        )}
                                                        {cameraSections.map(
                                                            (
                                                                singleCameraSection,
                                                                index
                                                            ) => (
                                                                <Col
                                                                    xs={24}
                                                                    md={24}
                                                                    className="gx-pl-0"
                                                                    key={
                                                                        singleCameraSection.key
                                                                    }
                                                                >
                                                                    {singleCameraSection.title !=
                                                                        '' && (
                                                                        <h3 className="gx-mt-3">
                                                                            {singleCameraSection.required && (
                                                                                <span
                                                                                    style={{
                                                                                        color: 'red',
                                                                                    }}
                                                                                >
                                                                                    {' '}
                                                                                    *{' '}
                                                                                </span>
                                                                            )}
                                                                            {
                                                                                singleCameraSection.title
                                                                            }
                                                                            <hr className="gx-bg-dark"></hr>
                                                                        </h3>
                                                                    )}
                                                                    {/* For debugging */}
                                                                    {/* {
                                                                                this.state.cameraRecordingsBySection && 
                                                                                <Alert 
                                                                                    message={JSON.stringify(prefillFormData.camera_files?.[singleCameraSection.key])}
                                                                                    type="info" />
                                                                            } */}
                                                                    <CameraInput
                                                                        required={
                                                                            singleCameraSection.required
                                                                        }
                                                                        authToken={http_utils.getAuthToken()}
                                                                        prefixDomain={http_utils.getCDNDomain()}
                                                                        initialFiles={
                                                                            this
                                                                                .state
                                                                                .editMode
                                                                                ? prefillFormData
                                                                                      .camera_files?.[
                                                                                      singleCameraSection
                                                                                          .key
                                                                                  ]
                                                                                : []
                                                                        }
                                                                        onFilesChanged={(
                                                                            files
                                                                        ) => {
                                                                            this.onCameraFilesChanged(
                                                                                singleCameraSection.key,
                                                                                files
                                                                            );
                                                                        }}
                                                                        onReadyStatusChange={(
                                                                            isReady
                                                                        ) => {
                                                                            this.onCameraFileUploaderReadyChange(
                                                                                singleCameraSection.key,
                                                                                isReady
                                                                            );
                                                                        }}
                                                                        readOnly={
                                                                            singleCameraSection.disabled
                                                                        }
                                                                    />
                                                                </Col>
                                                            )
                                                        )}
                                                        {fileSections.map(
                                                            (
                                                                singleFileSection,
                                                                index
                                                            ) => (
                                                                <Col
                                                                    xs={24}
                                                                    md={24}
                                                                    className="gx-pl-0"
                                                                    key={
                                                                        singleFileSection.key
                                                                    }
                                                                >
                                                                    {singleFileSection.title !=
                                                                        '' && (
                                                                        <h3 className="gx-mt-3">
                                                                            {singleFileSection.required && (
                                                                                <span
                                                                                    style={{
                                                                                        color: 'red',
                                                                                    }}
                                                                                >
                                                                                    {' '}
                                                                                    *{' '}
                                                                                </span>
                                                                            )}
                                                                            {
                                                                                singleFileSection.title
                                                                            }
                                                                            <hr className="gx-bg-dark"></hr>
                                                                        </h3>
                                                                    )}
                                                                    {/* For debugging */}
                                                                    {/* {
                                                                                this.state.filesBySection && 
                                                                                <Alert 
                                                                                    message={JSON.stringify(this.state.filesBySection)}
                                                                                    type="info" />
                                                                            } */}
                                                                    <Form.Item
                                                                        name={
                                                                            'file_uploads'
                                                                        }
                                                                    >
                                                                        <S3Uploader
                                                                            // className="gx-w-50"
                                                                            // demoMode
                                                                            required={
                                                                                singleFileSection.required
                                                                            }
                                                                            disabled={
                                                                                singleFileSection.disabled
                                                                            }
                                                                            maxColSpan={
                                                                                6
                                                                            }
                                                                            authToken={http_utils.getAuthToken()}
                                                                            prefixDomain={http_utils.getCDNDomain()}
                                                                            totalFiles={
                                                                                prefillFormData
                                                                                    ?.attachments?.[
                                                                                    singleFileSection
                                                                                        .key
                                                                                ]
                                                                            }
                                                                            onFilesChanged={(
                                                                                files
                                                                            ) => {
                                                                                this.onFilesChanged(
                                                                                    singleFileSection.key,
                                                                                    files
                                                                                );
                                                                            }}
                                                                            onReadyStatusChanged={(
                                                                                isReady
                                                                            ) => {
                                                                                this.onFileUploaderReadyChange(
                                                                                    singleFileSection.key,
                                                                                    isReady
                                                                                );
                                                                            }}
                                                                            initialFiles={
                                                                                this
                                                                                    .state
                                                                                    .editMode
                                                                                    ? prefillFormData
                                                                                          .attachments?.[
                                                                                          singleFileSection
                                                                                              .key
                                                                                      ]
                                                                                    : []
                                                                            }
                                                                            customPreviewHeight="100%"
                                                                            customFileIconMaxWidth="40px"
                                                                            compConfig={{
                                                                                name: 'servicesItemEditor',
                                                                            }}
                                                                        />
                                                                    </Form.Item>
                                                                </Col>
                                                            )
                                                        )}
                                                        {this.state
                                                            .editMode && (
                                                            <Col
                                                                xs={24}
                                                                className="gx-pl-0"
                                                            >
                                                                <SubtaskFilesSection
                                                                    srvc_req_id={
                                                                        editorItem?.id
                                                                    }
                                                                    srvc_type_id={
                                                                        viewData?.srvc_type_id
                                                                    }
                                                                    isCustAcces={
                                                                        this
                                                                            .props
                                                                            .isCustAcces
                                                                    }
                                                                />
                                                            </Col>
                                                        )}
                                                    </Row>
                                                    {/* </Form>  */}
                                                </Col>
                                                <Col
                                                    xs={24}
                                                    md={12}
                                                    className={`gx-py-1 gx-px-0 ${!editMode ? 'gx-bg-light-grey' : ''}`}
                                                >
                                                    {editMode &&
                                                        this.isProjectNature() &&
                                                        this.noInitialDeployment() &&
                                                        this.hasAccessToEditDeployment() && (
                                                            <div className="gx-px-md-3 gx-px-0">
                                                                <Deployments
                                                                    viewData={
                                                                        this
                                                                            .state
                                                                            .viewData
                                                                    }
                                                                    deploymentConfigData={this.getDeploymentConfigData()}
                                                                    reqData={this.getRequestData()}
                                                                    calendarData={
                                                                        this
                                                                            .state
                                                                            .calendarData
                                                                    }
                                                                    onChange={() => {
                                                                        this.refreshSelf();
                                                                    }}
                                                                    filters={
                                                                        this
                                                                            .props
                                                                            .filters
                                                                    }
                                                                    userRestricted={
                                                                        userRestricted
                                                                    }
                                                                />
                                                                <hr></hr>
                                                            </div>
                                                        )}
                                                    {editMode &&
                                                        !this.isProjectNature() && (
                                                            <div className="gx-px-md-3 gx-px-0">
                                                                <div className="gx-d-flex gx-px-1 gx-justify-content-between">
                                                                    <h3 className="gx-mt-1">
                                                                        {/* <i className="icon icon-basic-calendar gx-mr-2"></i> */}
                                                                        Subtasks
                                                                    </h3>
                                                                    {this.props
                                                                        ?.editorItem
                                                                        ?.sbtsks_meta &&
                                                                        ConfigHelpers.isServiceProvider() &&
                                                                        this.displayAvgGaiRatingOfSubTasks(
                                                                            this
                                                                                .props
                                                                                ?.editorItem
                                                                                ?.sbtsks_meta
                                                                        )}
                                                                </div>
                                                                <CompactSubtasksList
                                                                    reqSrvcDate={
                                                                        reqSrvcDate
                                                                    }
                                                                    srvcReqId={
                                                                        editorItem?.id
                                                                    }
                                                                    srvc_type_id={
                                                                        this
                                                                            .props
                                                                            .srvcDetails
                                                                            ?.srvc_id
                                                                    }
                                                                    srvcTypeId={
                                                                        viewData?.srvc_type_id
                                                                    }
                                                                    sbtsk_types={
                                                                        viewData
                                                                            ?.form_data
                                                                            ?.sbtsk_types
                                                                    }
                                                                    srvcReqTitle={
                                                                        viewData
                                                                            ?.form_data
                                                                            ?.title
                                                                    }
                                                                    onDataModified={(
                                                                        entry_id
                                                                    ) => {
                                                                        this.tellParentToRefreshList(
                                                                            editorItem?.id
                                                                        );
                                                                        this.refreshSelf(
                                                                            entry_id
                                                                        );
                                                                    }}
                                                                    roleList={
                                                                        viewData?.role_list
                                                                    }
                                                                    filters={
                                                                        this
                                                                            .props
                                                                            .filters
                                                                    }
                                                                    spConfigData={
                                                                        this
                                                                            .state
                                                                            .viewData
                                                                            ?.form_data
                                                                            ?.sp_config_data?.[0]
                                                                    }
                                                                    reqStartTime={
                                                                        reqStartTime
                                                                    }
                                                                    reqEndTime={
                                                                        reqEndTime
                                                                    }
                                                                    isCustomerRequests={
                                                                        this
                                                                            .props
                                                                            ?.isCustomerRequests
                                                                    }
                                                                    isCustAcces={
                                                                        this
                                                                            .props
                                                                            .isCustAcces
                                                                    }
                                                                    srvcConfigData={
                                                                        this
                                                                            .props
                                                                            ?.srvcConfigData
                                                                    }
                                                                    forQuickAssign={
                                                                        this
                                                                            .props
                                                                            ?.forQuickAssign
                                                                    }
                                                                    userRestricted={
                                                                        userRestricted
                                                                    }
                                                                />
                                                                <hr></hr>
                                                            </div>
                                                        )}
                                                    {editMode && (
                                                        <div className="gx-px-md-3 gx-px-0">
                                                            {this.isProjectNature() && (
                                                                <>
                                                                    <h3>
                                                                        Calendar
                                                                    </h3>
                                                                    <div className="gx-ml-0">
                                                                        <SrvcReqCalendar
                                                                            // demoMode
                                                                            srvcReqId={
                                                                                editorItem?.id
                                                                            }
                                                                            isCustomerRequests={this.isCustomerRequests()}
                                                                            // For daily status update
                                                                            // srvcConfigData={this.props.srvcConfigData}
                                                                            srvcConfigData={this.getDeploymentConfigData()}
                                                                            srvcConfigDataFrPrvdr={this.getConfigDataFrSrvcPrvdr()}
                                                                            reqData={this.getRequestData()}
                                                                            viewData={
                                                                                this
                                                                                    .state
                                                                                    .viewData
                                                                            }
                                                                            calendarData={
                                                                                this
                                                                                    .state
                                                                                    .calendarData
                                                                            }
                                                                            urlToSubmitFrUpdates={
                                                                                this
                                                                                    .submitUrl +
                                                                                '/' +
                                                                                this
                                                                                    .props
                                                                                    .editorItem
                                                                                    .id
                                                                            }
                                                                            onDailyStatusChange={() => {
                                                                                this.refreshSelf();
                                                                            }}
                                                                            onDayWiseBillingChange={() => {
                                                                                this.onDayWiseBillingChangeSubmitForm();
                                                                            }}
                                                                            onChange={() => {
                                                                                this.refreshSelf();
                                                                            }}
                                                                            setCalnedarData={(
                                                                                value
                                                                            ) => {
                                                                                this.setCalnedarData(
                                                                                    value
                                                                                );
                                                                            }}
                                                                            isSrvcReqLock={getIsSrvcReqLock(
                                                                                prefillFormData,
                                                                                this.isOrgSrvcPrvdr()
                                                                            )}
                                                                            filters={
                                                                                this
                                                                                    .props
                                                                                    .filters
                                                                            }
                                                                            isCustAcces={
                                                                                this
                                                                                    .props
                                                                                    .isCustAcces
                                                                            }
                                                                            userRestricted={
                                                                                userRestricted
                                                                            }
                                                                        />
                                                                    </div>
                                                                    <hr />
                                                                </>
                                                            )}
                                                            <h3 className="gx-mt-1 gx-mb-3">
                                                                Transition
                                                                overview
                                                            </h3>
                                                            <Tabs
                                                                onChange={(
                                                                    currentTab
                                                                ) =>
                                                                    this.setTransitionOverviewActiveTab(
                                                                        currentTab
                                                                    )
                                                                }
                                                                defaultActiveKey={
                                                                    this.state
                                                                        .transitionOverviewActiveTab
                                                                }
                                                            >
                                                                <Tabs.TabPane
                                                                    tab="Latest"
                                                                    key="latest"
                                                                    // forceRender
                                                                    className="gx-pl-1"
                                                                >
                                                                    <div>
                                                                        <StatusHistory
                                                                            data={
                                                                                viewData
                                                                                    ?.form_data
                                                                                    ?.status_transitions
                                                                            }
                                                                        />
                                                                    </div>
                                                                </Tabs.TabPane>
                                                                <Tabs.TabPane
                                                                    tab="Lifetime"
                                                                    key="lifetime"
                                                                    // forceRender
                                                                    className="gx-pl-1"
                                                                >
                                                                    <div>
                                                                        <LifetimeStatusHistory
                                                                            srvc_id={
                                                                                this
                                                                                    .props
                                                                                    ?.srvcDetails
                                                                                    ?.srvc_id
                                                                            }
                                                                            srvc_req_id={
                                                                                this
                                                                                    .props
                                                                                    .editorItem
                                                                                    ?.id
                                                                            }
                                                                        />
                                                                    </div>
                                                                </Tabs.TabPane>
                                                            </Tabs>

                                                            <hr></hr>
                                                            <h3 className="gx-mt-1 gx-mb-3">
                                                                {/* <i className="icon icon-basic-calendar gx-mr-2"></i> */}
                                                                Timeline
                                                            </h3>

                                                            <TimelineCard
                                                                srvcDetails={
                                                                    this.props
                                                                        .srvcDetails
                                                                }
                                                                srvcReqId={
                                                                    editorItem?.id
                                                                }
                                                                sbtsk_types={
                                                                    viewData
                                                                        ?.form_data
                                                                        ?.sbtsk_types
                                                                }
                                                                onDataModified={(
                                                                    entry_id
                                                                ) =>
                                                                    this.tellParentToRefreshList(
                                                                        editorItem?.id
                                                                    )
                                                                }
                                                                onDataSubmitComment={(
                                                                    data
                                                                ) =>
                                                                    this.submitForm(
                                                                        data,
                                                                        true
                                                                    )
                                                                }
                                                                formDataMeta={this.getAllFieldsMeta()}
                                                                srvcConfigData={
                                                                    this.props
                                                                        .srvcConfigData
                                                                }
                                                                subtaskConfigData={
                                                                    viewData
                                                                        ?.form_data
                                                                        ?.subtaskConfigData
                                                                }
                                                                custName={this.getCustName()}
                                                                spConfigData={
                                                                    this.state
                                                                        .viewData
                                                                        ?.form_data
                                                                        ?.sp_config_data?.[0]
                                                                        ?.settings_data
                                                                }
                                                                feedback_data={
                                                                    userFeedbackData
                                                                }
                                                                allSrvcPrvdrs={
                                                                    this.props
                                                                        .all_srvc_prvdrs
                                                                }
                                                            />
                                                        </div>
                                                    )}
                                                    {!editMode &&
                                                        (this.state
                                                            .autoFillCustId ? (
                                                            <CustomerHistory
                                                                cust_mobile={
                                                                    this.state
                                                                        .custMobNoFrHis
                                                                }
                                                            />
                                                        ) : (
                                                            <h4 className="gx-text-grey gx-mb-1 gx-ml-5 gx-mb-3 ">
                                                                <HistoryOutlined />
                                                                <span className="gx-ml-2">
                                                                    Customer
                                                                    History{' '}
                                                                    <NoData />
                                                                </span>
                                                            </h4>
                                                        ))}
                                                </Col>
                                            </Row>
                                        </Tabs.TabPane>
                                        {this.isProjectNature() &&
                                            viewData?.form_data
                                                ?.allow_to_see_line_item_tab && (
                                                <Tabs.TabPane
                                                    tab="Line items"
                                                    key="line_items"
                                                    // forceRender
                                                    className="gx-pl-1"
                                                >
                                                    <this.LineItemsContainer />
                                                </Tabs.TabPane>
                                            )}
                                        {viewData?.form_data
                                            ?.allow_to_see_sp_payout_tab &&
                                            this.isSpPayoutConfigEnabled() && (
                                                <Tabs.TabPane
                                                    tab="SP Payouts"
                                                    key="sp_payouts"
                                                    // forceRender
                                                    className="gx-pl-1"
                                                >
                                                    <this.spPayoutsContainer />
                                                </Tabs.TabPane>
                                            )}
                                        {!ConfigHelpers.isServiceProvider() &&
                                            !this.isCustomerRequests() &&
                                            this.isBillingEnable() &&
                                            editMode && (
                                                <Tabs.TabPane
                                                    tab="Billing"
                                                    key="billing"
                                                    forceRender={
                                                        this.state
                                                            .updateBillingData
                                                    }
                                                    className="gx-pl-1"
                                                >
                                                    <Billing
                                                        srvcConfigData={this.getSrvcTypeConfigData()}
                                                        lineItemConfig={this.getLineItemConfig()}
                                                        srvcReqLineItemsData={
                                                            this.state
                                                                .lineItemsData ||
                                                            prefillFormData?.line_items ||
                                                            {}
                                                        }
                                                        srvcReqAdditionalLineItemsData={
                                                            this.state
                                                                .additionalBillingItem ||
                                                            prefillFormData?.additional_billing_items ||
                                                            {}
                                                        }
                                                        srvcReqFinalBillingData={
                                                            this.state
                                                                .finalBillingItem ||
                                                            prefillFormData?.final_billing_items ||
                                                            {}
                                                        }
                                                        serviceReqFormData={
                                                            viewData?.form_data
                                                        }
                                                        calendarsData={
                                                            this.state
                                                                .calendarData
                                                        }
                                                        customFieldsFrmConfig={this.getCustomFieldsFrmConfig()}
                                                        onChange={(
                                                            newData,
                                                            type,
                                                            callback
                                                        ) => {
                                                            if (
                                                                type ==
                                                                'additional_billing_items'
                                                            ) {
                                                                this.setState({
                                                                    additionalBillingItem:
                                                                        newData,
                                                                });
                                                            } else if (
                                                                type ==
                                                                'final_billing_items'
                                                            ) {
                                                                this.setState({
                                                                    finalBillingItem:
                                                                        newData,
                                                                });
                                                            }
                                                        }}
                                                        onDataSubmitDisApproval={(
                                                            newData
                                                        ) => {
                                                            // console.log("newData approval",newData)
                                                            this.formRef.current.setFieldsValue(
                                                                {
                                                                    send_for_discount_approval: false,
                                                                }
                                                            );
                                                            this.submitForm(
                                                                newData,
                                                                false,
                                                                true
                                                            );
                                                        }}
                                                        isFormSubmitting={
                                                            this.state
                                                                .isFormSubmitting
                                                        }
                                                        formRef={
                                                            this.formRef
                                                                ?.current
                                                        }
                                                        onDataSubmitForReqLock={(
                                                            newData
                                                        ) => {
                                                            this.submitForm(
                                                                newData
                                                            );
                                                        }}
                                                        isSrvcReqLock={getIsSrvcReqLock(
                                                            prefillFormData,
                                                            this.isOrgSrvcPrvdr()
                                                        )}
                                                        onDataSubmitForSyncPrc={(
                                                            newData
                                                        ) => {
                                                            this.submitForm(
                                                                newData,
                                                                false,
                                                                true
                                                            );
                                                            this.tellParentToRefreshList(
                                                                editorItem?.id
                                                            );
                                                            this.refreshSelf();
                                                            this.setCurrentTab(
                                                                'billing'
                                                            );
                                                        }}
                                                        onDataSubmitSendFrBilling={(
                                                            newData
                                                        ) => {
                                                            this.submitForm(
                                                                newData,
                                                                false,
                                                                true
                                                            );
                                                        }}
                                                        srvc_id={
                                                            this.props.srvc_id
                                                        }
                                                        activeFilters={
                                                            this.props.filters
                                                        }
                                                        title={this.getTitle()}
                                                    />
                                                </Tabs.TabPane>
                                            )}
                                        {this.isPrvdrBillingEnable() &&
                                            this.haveAccessFrBilling() &&
                                            editMode && (
                                                <Tabs.TabPane
                                                    tab="SP Billing"
                                                    key="sp_billing"
                                                    forceRender={
                                                        this.state
                                                            .updateBillingData
                                                    }
                                                    className="gx-pl-1"
                                                >
                                                    <Billing
                                                        srvcConfigData={this.getConfigDataFrSrvcPrvdr()}
                                                        lineItemConfig={this.spGetLineItemConfig()}
                                                        srvcReqLineItemsData={
                                                            this.state
                                                                .splineItemsData ||
                                                            prefillFormData?.sp_line_items ||
                                                            {}
                                                        }
                                                        srvcReqDeductionData={
                                                            this.state
                                                                .spDeductionData ||
                                                            prefillFormData?.sp_deduction_data ||
                                                            {}
                                                        }
                                                        srvcReqAdditionalLineItemsData={
                                                            this.state
                                                                .spAdditionalBillingItem ||
                                                            prefillFormData?.sp_additional_billing_items ||
                                                            {}
                                                        }
                                                        srvcReqFinalBillingData={
                                                            this.state
                                                                .spFinalBillingItem ||
                                                            prefillFormData?.sp_final_billing_items ||
                                                            {}
                                                        }
                                                        serviceReqFormData={
                                                            viewData?.form_data
                                                        }
                                                        calendarsData={
                                                            this.state
                                                                .calendarData
                                                        }
                                                        isServiceProviderTab
                                                        customFieldsFrmConfig={this.getCustomFieldsFrmConfig()}
                                                        onChange={(
                                                            newData,
                                                            type,
                                                            callback
                                                        ) => {
                                                            if (
                                                                type ==
                                                                'additional_billing_items'
                                                            ) {
                                                                this.setState({
                                                                    spAdditionalBillingItem:
                                                                        newData,
                                                                });
                                                            } else if (
                                                                type ==
                                                                'deduction_items'
                                                            ) {
                                                                this.setState({
                                                                    spDeductionData:
                                                                        newData,
                                                                });
                                                            } else if (
                                                                type ==
                                                                'final_billing_items'
                                                            ) {
                                                                this.setState({
                                                                    spFinalBillingItem:
                                                                        newData,
                                                                });
                                                            }
                                                        }}
                                                        readOnly={
                                                            !ConfigHelpers.isServiceProvider()
                                                                ? true
                                                                : false
                                                        }
                                                        onDataSubmitDisApproval={(
                                                            newData
                                                        ) => {
                                                            // console.log("newData approval",newData)
                                                            this.formRef.current.setFieldsValue(
                                                                {
                                                                    sp_send_for_discount_approval: false,
                                                                }
                                                            );
                                                            this.submitForm(
                                                                newData,
                                                                false,
                                                                true
                                                            );
                                                        }}
                                                        isFormSubmitting={
                                                            this.state
                                                                .isFormSubmitting
                                                        }
                                                        formRef={
                                                            this.formRef.current
                                                        }
                                                        onDataSubmitForReqLock={(
                                                            newData
                                                        ) => {
                                                            this.submitForm(
                                                                newData
                                                            );
                                                        }}
                                                        isSrvcReqLock={getIsSrvcReqLock(
                                                            prefillFormData,
                                                            this.isOrgSrvcPrvdr()
                                                        )}
                                                        onDataSubmitForSyncPrc={(
                                                            newData
                                                        ) => {
                                                            this.submitForm(
                                                                newData,
                                                                false,
                                                                true
                                                            );
                                                            this.tellParentToRefreshList(
                                                                editorItem?.id
                                                            );
                                                            this.refreshSelf();
                                                            this.setCurrentTab(
                                                                'sp_billing'
                                                            );
                                                        }}
                                                        onDataSubmitSendFrBilling={(
                                                            newData
                                                        ) => {
                                                            this.submitForm(
                                                                newData,
                                                                false,
                                                                true
                                                            );
                                                        }}
                                                        srvc_id={
                                                            this.props.srvc_id
                                                        }
                                                        activeFilters={
                                                            this.props.filters
                                                        }
                                                        title={this.getTitle()}
                                                    />
                                                </Tabs.TabPane>
                                            )}
                                        {!ConfigHelpers.isServiceProvider() &&
                                            !this.isCustomerRequests() &&
                                            (this.isRatingEnable() ||
                                                this.state.viewData?.form_data
                                                    ?.all_ratings?.length >
                                                    0) &&
                                            editMode && (
                                                <Tabs.TabPane
                                                    tab="Ratings"
                                                    key="ratings"
                                                    // forceRender
                                                    className="gx-pl-1"
                                                >
                                                    <Rating
                                                        key={'brand_ratings'}
                                                        isRatingEnabled={this.isRatingEnable()}
                                                        srvcConfigData={
                                                            this.props
                                                                .srvcConfigData
                                                        }
                                                        ratingDetails={this.getRatingDetails(
                                                            'brand',
                                                            this.props
                                                                .srvcConfigData,
                                                            this.props
                                                                .srvcConfigData
                                                                ?.srvc_type_nature
                                                        )}
                                                        srvcOrVerticalNature={
                                                            this.props
                                                                .srvcConfigData
                                                                ?.srvc_type_nature
                                                        }
                                                        srvcReqData={
                                                            this.state.viewData
                                                                ?.form_data
                                                                ?.form_data
                                                        }
                                                        allRatings={
                                                            this.state.viewData
                                                                ?.form_data
                                                                ?.all_ratings
                                                        }
                                                        onChange={() => {
                                                            this.refreshSelf();
                                                        }}
                                                    />
                                                </Tabs.TabPane>
                                            )}
                                        {ConfigHelpers.isServiceProvider() &&
                                            (this.isSpRatingsEnable() ||
                                                this.state.viewData?.form_data
                                                    ?.sp_all_ratings?.length >
                                                    0) &&
                                            editMode && (
                                                <Tabs.TabPane
                                                    tab="SP Ratings"
                                                    key="sp_ratings"
                                                    // forceRender
                                                    className="gx-pl-1"
                                                >
                                                    <Rating
                                                        key={'sp_ratings'}
                                                        srvcPrvdr
                                                        isRatingEnabled={this.isSpRatingsEnable()}
                                                        ratingDetails={this.getRatingDetailsFrSrvcPrvdr(
                                                            'srvcPrvdr',
                                                            this.getConfigDataFrSrvcPrvdr(),
                                                            this.props
                                                                .srvcConfigData
                                                                ?.srvc_type_nature
                                                        )}
                                                        srvcOrVerticalNature={
                                                            this.getConfigDataFrSrvcPrvdr()
                                                                ?.vertical_nature
                                                        }
                                                        srvcReqData={
                                                            this.state.viewData
                                                                ?.form_data
                                                                ?.form_data
                                                        }
                                                        allRatings={
                                                            this.state.viewData
                                                                ?.form_data
                                                                ?.sp_all_ratings
                                                        }
                                                        onChange={() => {
                                                            this.refreshSelf();
                                                        }}
                                                    />
                                                </Tabs.TabPane>
                                            )}
                                        {showProfitLossTab &&
                                            !this.isProjectNature() && (
                                                <Tabs.TabPane
                                                    tab="P&L"
                                                    key="profitLossTab"
                                                    // forceRender
                                                    className="gx-px-1"
                                                >
                                                    <ProfitAndLoss
                                                        spConfigData={this.getConfigDataFrSrvcPrvdr()}
                                                        srvcReqId={
                                                            editorItem?.id
                                                        }
                                                        srvcTypeId={
                                                            this.props
                                                                .srvcDetails
                                                                ?.srvc_id
                                                        }
                                                        srvcConfigData={
                                                            this.props
                                                                .srvcConfigData
                                                        }
                                                        srvcReqData={{
                                                            ...this.state
                                                                .viewData
                                                                ?.form_data
                                                                ?.form_data,
                                                            latestOpenStatus,
                                                            latestClosedStatus,
                                                        }}
                                                        locGrpsName={this.getLocationGrpsName()}
                                                        allAuthoritiesOfVertical={this.getSPAuthoritySelectorMeta()}
                                                        orgNickName={
                                                            this.state.viewData
                                                                ?.form_data
                                                                ?.org_nick_name
                                                        }
                                                        roleList={
                                                            this.state.viewData
                                                                ?.role_list
                                                        }
                                                    />
                                                </Tabs.TabPane>
                                            )}
                                        {showProjectProfitLossTab &&
                                            this.isProjectNature() && (
                                                <Tabs.TabPane
                                                    tab="P&L"
                                                    key="profitLossTab"
                                                    className="gx-px-1"
                                                >
                                                    <ProjectProfitAndLoss
                                                        spConfigData={this.getConfigDataFrSrvcPrvdr()}
                                                        srvcReqId={
                                                            editorItem?.id
                                                        }
                                                        srvcTypeId={
                                                            this.props
                                                                .srvcDetails
                                                                ?.srvc_id
                                                        }
                                                        srvcConfigData={
                                                            this.props
                                                                .srvcConfigData
                                                        }
                                                        srvcReqData={{
                                                            ...this.state
                                                                .viewData
                                                                ?.form_data
                                                                ?.form_data,
                                                            latestOpenStatus,
                                                            latestClosedStatus,
                                                        }}
                                                        locGrpsName={this.getLocationGrpsName()}
                                                        allAuthoritiesOfVertical={this.getSPAuthoritySelectorMeta()}
                                                        orgNickName={
                                                            this.state.viewData
                                                                ?.form_data
                                                                ?.org_nick_name
                                                        }
                                                        roleList={
                                                            this.state.viewData
                                                                ?.role_list
                                                        }
                                                    />
                                                </Tabs.TabPane>
                                            )}
                                    </Tabs>
                                    <div className="gx-mt-5">
                                        {!allFileUploadersReady && (
                                            <Spin></Spin>
                                        )}
                                        {(userRights?.includes('CREATE') ||
                                            userRights?.includes('UPDATE') ||
                                            (ConfigHelpers.isServiceProvider() &&
                                                !ConfigHelpers.isUserOnfield())) &&
                                            ![
                                                'ratings',
                                                'sp_ratings',
                                                'profitLossTab',
                                            ].includes(selectedCurrentTab) && (
                                                <Button
                                                    type="primary"
                                                    htmlType="submit"
                                                    disabled={
                                                        isFormSubmitting ||
                                                        !allFileUploadersReady
                                                    }
                                                >
                                                    {editMode
                                                        ? 'Save'
                                                        : 'Submit'}
                                                </Button>
                                            )}

                                        {isFormSubmitting ? (
                                            <div className="gx-loader-view gx-loader-position">
                                                <CircularProgress />
                                            </div>
                                        ) : null}
                                        {error ? (
                                            <p className="gx-text-red">
                                                {error}
                                            </p>
                                        ) : null}
                                    </div>
                                </Form>
                            </Col>
                        </Row>
                    </>
                )}
            </Modal>
        ) : (
            <></>
        );
    }
}

export default ItemEditor;
