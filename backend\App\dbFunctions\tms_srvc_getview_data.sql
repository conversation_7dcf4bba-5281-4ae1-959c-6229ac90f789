CREATE OR REPLACE FUNCTION public.tms_srvc_getview_data(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	org_id_ integer;
	usr_id_ uuid;
	srvc_type_id_ integer;
	srvc_req_id_ bigint;

	--temp
	_statuses json;
	resp_data json;
	form_data_ json;
	_config_data json;
    role_wise_authorities_users_list json;
   	srvc_authorities int[];
   	login_usr_org_type text;
   	srvc_type_id_org int;
    usr_org_id int;
    users_list json;
	sbtsk_config_data json;
	reportees_list json;
	role_list json;
	srvc_prvdr_authorities_roles int[];
	srvc_prvdr_role_wise_authorities_users_list json;
	sp_config_data json;
	_existing_srvc_form_data json;
	role_list_vs_users json;
    srvc_req_loc_grp_id int[];
    is_srvc_prvdr bool;
	is_capacity_module_enabled bool default false;

	begin
		
		status = false;
		message = 'Internal_error';
	
		form_data_ = form_data;
		org_id_ = json_extract_path_text(form_data,'org_id');
		usr_id_ = json_extract_path_text(form_data,'usr_id');
		srvc_type_id_ = json_extract_path_text(form_data,'srvc_type_id');
		srvc_req_id_ = form_data->>'entry_id';
	
        is_srvc_prvdr = tms_hlpr_is_org_srvc_prvdr(org_id_);
	-- get srvc req loc group ids
	    srvc_req_loc_grp_id = tms_hlpr_get_loc_group_id_by_srvc_req_id_fr_sp(srvc_req_id_::int);
	
		if srvc_req_id_ is not null then
			select srvc_req_.form_data::json 
			  from cl_tx_srvc_req as srvc_req_
			 where srvc_req_.db_id  = srvc_req_id_
			  into _existing_srvc_form_data;
		end if;
		_statuses = array_to_json(array(
		    	select jsonb_build_object(
		    				'value',srvc_statuses.status_key ,
		    				'title',srvc_statuses.title,
		    				'label',srvc_statuses.title,
		    				'color',srvc_statuses.color
		    		   ) 
			      from public.cl_cf_srvc_statuses as srvc_statuses
			     inner join cl_cf_service_types as srvc_types
			        on srvc_types.service_type_id = srvc_type_id_
				 where srvc_statuses.srvc_id = srvc_type_id_
				 order by srvc_statuses.db_id 
		));	
	
		if json_array_length(_statuses) > 0 then 
		    resp_data =  json_build_object('statuses',_statuses);
		end if;
	
	
		--START FOR ROLE_WISE_AUTHORITIES_USERS_LIST
		
		usr_org_id = org_id_;
	
	    --get what is the org_type for login user
		select org_type.org_type 
	 	  from cl_tx_orgs as org_type
	 	 where org_type.org_id = org_id_
	 	  into login_usr_org_type;
	 	 
	 	--if login user org_type is ORG_TYPE_SRVC_PRVDR
	 	if login_usr_org_type = 'ORG_TYPE_SRVC_PRVDR' then
	 	 
	 	 	--get srvc_type_id belongs to which org
			select srvc_type_org.org_id 
			  from cl_cf_service_types as srvc_type_org
			 where srvc_type_org.service_type_id = srvc_type_id_
			  into srvc_type_id_org;
			 
			 if srvc_type_id_org <> org_id_ then 
				 usr_org_id = srvc_type_id_org;
			 end if;

	 	end if;

 		--Get srvc_prvdr authotities
	 	srvc_prvdr_role_wise_authorities_users_list = array_to_json(array(
		    	select jsonb_build_object(
		    	            'role_id', roles.role_id,
							'key', 'authority_' || (roles.role_id::text) ,
							'label', (roles.name_details).title ,
							'widget','select',
							'is_loc_grp_filteration_enabled_fr_sp',
												case 
													when cardinality(srvc_req_loc_grp_id) = 0 then false -- if loc grop not present then filteration will not happen as well
													else tms_hlpr_get_is_loc_group_filteration_enabled_fr_role(srvc_prvdr_authorities.settings_data,roles.role_id) is true
												 end,
							'options',jsonb_agg(
								distinct 
					   			jsonb_build_object(
					   				'value', users.usr_id ,
									'label', users."name",
									'role_id', usr_roles.role_id,
									'is_loc_grp_matched_with_srvc_req',
												case 
										          when users.loc_group && srvc_req_loc_grp_id then true 
										          else false																			
											     end	
									
							    ) 
					   		)		
					   ) 
				  from public.cl_cf_roles as roles 
				 inner join cl_tx_orgs_settings as srvc_prvdr_authorities
				    on roles.role_id = any(array(select json_array_elements_text(srvc_prvdr_authorities.settings_data->'authority_id'))::int[])
				   and srvc_type_id_ = any(array(select json_array_elements_text(srvc_prvdr_authorities.settings_data->'srvc_type_id'))::int[])
				   and srvc_prvdr_authorities.settings_type = 'SP_CUSTOM_FIELDS'
				   and srvc_prvdr_authorities.org_id = org_id_
				  left join public.cl_tx_usr_roles as usr_roles
				    on usr_roles.role_id = roles.role_id 
			      inner join cl_tx_users as users
				    on users.usr_id = usr_roles.user_id 
				   and users.is_annonymous is false 
				   and users.is_active is true
				 where roles.org_id = org_id_
		         group by roles.role_id , srvc_prvdr_authorities.db_id
		         order by (roles.name_details).title asc
		));	
		resp_data = jsonb_set(resp_data::jsonb,'{srvc_prvdr_role_wise_authorities_users_list}',srvc_prvdr_role_wise_authorities_users_list::jsonb,true);
		 	
		if tms_hlpr_get_srvc_prvdr_authorities_config_data(srvc_type_id_) is not null then 
		    resp_data = jsonb_set(resp_data::jsonb,'{sp_authorities_config_data}',tms_hlpr_get_srvc_prvdr_authorities_config_data(srvc_type_id_)::jsonb,true);
		end if;
		
		srvc_authorities = array(
			select json_array_elements(cl_cf_service_types.form_data->'srvc_authorities')
			  from cl_cf_service_types 
			 where service_type_id = srvc_type_id_
			   and org_id = usr_org_id
		);
		
	    role_wise_authorities_users_list = array_to_json(array(
	    	select jsonb_build_object(
	    				'role_id', roles.role_id,
						'key', 'authority_' || (roles.role_id::text) ,
						'label', (roles.name_details).title ,
						'widget','select',
						'options',jsonb_agg(
							distinct 
				   			jsonb_build_object(
				   				'value', users.usr_id ,
								'label', users."name",
								'role_id', usr_roles.role_id
						    ) 
				   		)		
				   ) 
			  from public.cl_cf_roles as roles 
			 inner join public.cl_cf_role_access as role_access
	            on role_access.role_id = roles.role_id 
			  left join public.cl_tx_usr_roles as usr_roles
			    on usr_roles.role_id = roles.role_id
			 inner join cl_cf_service_types as srvc_types
    			on srvc_types.service_type_id = srvc_type_id_
		     inner join cl_tx_users as users
			    on users.usr_id = usr_roles.user_id 
			   and users.is_annonymous is false
			   and users.is_active is true
			   and (
				   		(srvc_types.form_data->>(roles.role_id || '_show_authorities_that_report_to_the_assigner'))::bool is not true 
				   		or
				   		(
				   			_existing_srvc_form_data is not null
				   			and
				   			_existing_srvc_form_data->>('authority_'|| roles.role_id ) is not null
				   			and
				   			_existing_srvc_form_data->>('authority_'|| roles.role_id ) <> ''
				   			and
			   				users.usr_id = (_existing_srvc_form_data->>('authority_'|| roles.role_id ))::uuid
			   			)
				   		or
				   		users.reporting_to = usr_id_
			   	   )
			 where roles.org_id = usr_org_id
			   and roles.role_id = any(srvc_authorities)
	         group by roles.role_id
	         order by (roles.name_details).title asc
		));	
	   
	    resp_data = jsonb_set(resp_data::jsonb,'{role_wise_authorities_users_list}',role_wise_authorities_users_list::jsonb,true);
	  
	   --END ROLE_WISE_AUTHORITIES_USERS_LIST
	 
	   --get users list
--	   	users_list = tms_hlpr_get_users_list_by_org(org_id_);
--	    resp_data = jsonb_set(resp_data::jsonb,'{users_list}',users_list::jsonb,true);
	   
	   --get reportees list
--	   	reportees_list = tms_hlpr_get_users_reporting_to(usr_id_, org_id_);
--	   	resp_data = jsonb_set(resp_data::jsonb,'{reportees_list}',reportees_list::jsonb,true);
	   
	   --get config_data for sbtsk
--	    sbtsk_config_data = array_to_json(array(
--	        select jsonb_build_object(
--                	'sbtsk_type_id', sbtsk_types.sbtsk_type_id ,
--                	'config_data', sbtsk_types.form_data 
--           		   ) 
--              from public.cl_cf_sbtsk_types as sbtsk_types
--             where sbtsk_types.org_id = org_id_
--          	   and sbtsk_types.is_active is true
--             group by sbtsk_types.sbtsk_type_id
--	    ));
--       
--      resp_data = jsonb_set(resp_data::jsonb,'{sbtsk_config_data}',sbtsk_config_data::jsonb,true);
      
      	--get role list
	    role_list = array_to_json(array(
	        select jsonb_build_object(
                	'value', roles.role_id ,
                	'label', (roles.name_details).title
           		   ) 
              from cl_cf_roles as roles
             where roles.org_id = org_id_
             group by roles.role_id
	    ));
      	resp_data = jsonb_set(resp_data::jsonb,'{role_list}',role_list::jsonb,true);
      
        --service prvdr config_data
		sp_config_data = array_to_json(array(
	    	select jsonb_build_object(
		    			'db_id',sp_custom_fields.db_id ,
	    				'settings_type',sp_custom_fields.settings_type,
	    				'settings_data',sp_custom_fields.settings_data
	    		   ) 
		      from public.cl_tx_orgs_settings as sp_custom_fields 
			  where (
				 		(
				 			is_srvc_prvdr is true  
				 			and sp_custom_fields.org_id = org_id_
				 		)	
				 		or is_srvc_prvdr is false
			 		)		 					   
			   and sp_custom_fields.settings_type = 'SP_CUSTOM_FIELDS'
			   and srvc_type_id_ = any(array(SELECT json_array_elements_text(json_extract_path(sp_custom_fields.settings_data,'srvc_type_id')))::integer[])
		));
        resp_data = jsonb_set(resp_data::jsonb,'{sp_config_data}',to_jsonb(sp_config_data),true);
        resp_data = jsonb_set(resp_data::jsonb,'{srvc_type_id}',to_jsonb(srvc_type_id_),true);
		
               
        role_list_vs_users = tms_hlpr_get_role_vs_users_fr_org(org_id_);
      	resp_data = jsonb_set(resp_data::jsonb,'{role_list_vs_users}',role_list_vs_users::jsonb,true);

		--Sending the below by default
      	status = true;
      	message = 'success';
   
	  	return json_build_object('status',status,'code',message,'data',resp_data);


	END;
$function$
;
